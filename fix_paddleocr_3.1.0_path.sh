#!/bin/bash

# PaddleOCR 3.1.0 模型路径修复脚本
# 修复PaddleOCR 3.1.0版本的模型路径问题

echo "🔧 PaddleOCR 3.1.0 模型路径修复"
echo "==============================="

echo "📋 问题说明:"
echo "  PaddleOCR 3.1.0 改变了模型查找逻辑"
echo "  不再使用 PADDLEOCR_HOME 环境变量"
echo "  硬编码使用 /root/.paddlex/official_models/ 路径"
echo "  需要将模型放到正确的位置"
echo ""

# 检查压缩文件
if [ ! -f "paddlex_models.tar.gz" ]; then
    echo "❌ paddlex_models.tar.gz 文件不存在"
    exit 1
fi

echo "✅ 发现模型文件: paddlex_models.tar.gz"

# 显示压缩文件结构
echo "📋 压缩文件结构:"
tar -tzf paddlex_models.tar.gz | head -15

# 停止当前容器
if docker ps | grep -q paddleocr-service; then
    echo ""
    echo "🛑 停止当前容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 重新构建镜像
echo ""
echo "🔨 重新构建镜像（使用PaddleOCR 3.1.0正确路径）..."
echo "模型将放置到 /root/.paddlex/ 目录"

start_time=$(date +%s)

if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 验证模型结构
echo ""
echo "🔍 验证PaddleX模型结构..."
echo "检查 /root/.paddlex/ 目录:"
docker run --rm paddleocr-service:arm64-latest find /root/.paddlex -type d | head -10

echo ""
echo "检查 official_models 目录:"
docker run --rm paddleocr-service:arm64-latest ls -la /root/.paddlex/official_models/ 2>/dev/null || echo "official_models目录不存在"

# 重新部署服务
echo ""
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 6g \
    --cpus 2 \
    --shm-size 1g \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 检查模型加载日志
echo ""
echo "📋 检查模型加载日志:"
docker logs paddleocr-service 2>&1 | grep -E "(PaddleX|official_models|预加载)" | tail -10

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 测试容器内命令行OCR
echo ""
echo "🧪 测试容器内命令行OCR功能..."

# 创建测试图片
python3 -c "
from PIL import Image, ImageDraw, ImageFont
import os

img = Image.new('RGB', (300, 100), color='white')
draw = ImageDraw.Draw(img)

try:
    font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', 20)
except:
    font = ImageFont.load_default()

draw.text((20, 30), 'Test OCR 3.1.0', fill='black', font=font)
img.save('test_ocr_310.png')
print('✅ 测试图片已创建')
" 2>/dev/null || echo "⚠️ 无法创建测试图片"

if [ -f "test_ocr_310.png" ]; then
    # 复制测试图片到容器
    docker cp test_ocr_310.png paddleocr-service:/app/
    
    echo "🔍 在容器内测试paddleocr命令..."
    docker exec paddleocr-service timeout 60 paddleocr ocr -i test_ocr_310.png --device cpu 2>&1 | tail -20
    
    if [ $? -eq 0 ]; then
        echo "✅ 容器内OCR命令测试成功"
    else
        echo "❌ 容器内OCR命令仍然失败"
        echo "可能需要进一步调试"
    fi
    
    # 清理测试文件
    rm -f test_ocr_310.png
    docker exec paddleocr-service rm -f /app/test_ocr_310.png 2>/dev/null || true
fi

# 测试API接口
echo ""
echo "🌐 测试OCR API接口..."
if [ -f "test_ocr_310.png" ]; then
    response=$(curl -s -X POST -F "file=@test_ocr_310.png" http://localhost:9527/ocr)
    
    if echo "$response" | grep -q "texts"; then
        echo "✅ OCR API测试成功"
        echo "识别结果: $response"
    else
        echo "❌ OCR API测试失败"
        echo "响应: $response"
    fi
else
    echo "⚠️ 跳过API测试（无测试图片）"
fi

echo ""
echo "🎉 PaddleOCR 3.1.0 路径修复完成！"
echo "=================================="
echo "✅ 模型已放置到 /root/.paddlex/ 目录"
echo "✅ 符合PaddleOCR 3.1.0的路径要求"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🔍 调试命令:"
echo "   查看模型目录: docker exec paddleocr-service ls -la /root/.paddlex/official_models/"
echo "   查看容器日志: docker logs paddleocr-service"
echo "   进入容器调试: docker exec -it paddleocr-service bash"
echo ""

echo "✅ 修复流程完成！"
