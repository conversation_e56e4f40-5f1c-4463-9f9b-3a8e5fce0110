#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版PaddleOCR Flask服务
使用subprocess调用paddleocr命令行工具，避免复杂的库导入问题
"""

import os
import tempfile
import uuid
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from ocr_subprocess import OCRSubprocessor

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 限制上传文件大小为1024MB

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg'}

# 初始化OCR处理器
ocr_processor = OCRSubprocessor()

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        # 测试OCR命令是否可用
        is_healthy = ocr_processor.test_ocr_command()
        if is_healthy:
            return jsonify({
                'status': 'ok',
                'ocr_available': True,
                'device': ocr_processor.get_device_info()
            })
        else:
            return jsonify({
                'status': 'warning',
                'ocr_available': False,
                'message': 'OCR command not available'
            }), 503
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Health check failed: {str(e)}'
        }), 500

@app.route('/ocr', methods=['POST'])
def ocr_recognition():
    """OCR识别接口"""
    print("🔥 收到OCR请求")
    print(f"请求方法: {request.method}")
    print(f"请求文件: {list(request.files.keys())}")

    # 检查是否有文件上传
    if 'file' not in request.files:
        print("❌ 没有找到文件字段")
        return jsonify({'error': '没有上传文件'}), 400

    file = request.files['file']

    # 检查文件名是否为空
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400

    # 检查文件类型是否允许
    if not allowed_file(file.filename):
        return jsonify({'error': f'不支持的文件类型，仅支持 {", ".join(ALLOWED_EXTENSIONS)}'}), 400

    # 生成唯一的临时文件名
    file_ext = os.path.splitext(file.filename)[1].lower()
    temp_filename = f"ocr_{uuid.uuid4().hex}{file_ext}"
    temp_file_path = os.path.join(tempfile.gettempdir(), temp_filename)

    try:
        # 保存上传的文件到临时目录
        file.save(temp_file_path)
        print(f"文件已保存到: {temp_file_path}")

        # 验证文件是否正确保存
        if not os.path.exists(temp_file_path):
            return jsonify({'error': '文件保存失败'}), 500

        file_size = os.path.getsize(temp_file_path)
        print(f"文件大小: {file_size} 字节")

        if file_size == 0:
            return jsonify({'error': '上传的文件为空'}), 400

        try:
            # 进行OCR识别
            print(f"开始处理文件: {file.filename}")
            print(f"临时文件路径: {temp_file_path}")
            print(f"文件是否存在: {os.path.exists(temp_file_path)}")

            result = ocr_processor.process_file(temp_file_path)
            print(f"处理完成，结果类型: {type(result)}")
            print(f"处理结果: {result}")

            # 检查结果是否有效
            if result is None:
                print("⚠️  结果为None")
                return jsonify({
                    'texts': [],
                    'warning': '未检测到文本内容'
                })

            # 检查是否有错误
            if isinstance(result, dict) and "error" in result:
                print(f"❌ OCR返回错误: {result['error']}")
                return jsonify({
                    'error': result["error"]
                }), 500

            # 验证结果可以JSON序列化
            try:
                import json
                json_str = json.dumps(result, ensure_ascii=False)
                print("✅ JSON序列化验证通过")
            except Exception as json_e:
                print(f"❌ JSON序列化失败: {json_e}")
                return jsonify({
                    'error': f'结果序列化失败: {str(json_e)}'
                }), 500

            # 返回提取的文本内容
            response_data = {
                'texts': result,
                'file_processed': file.filename,
                'temp_file': temp_filename
            }
            print(f"✅ 准备返回响应: {response_data}")
            return jsonify(response_data)

        except Exception as e:
            print(f"❌ OCR处理异常: {str(e)}")
            print(f"异常类型: {type(e)}")
            import traceback
            print("完整异常堆栈:")
            traceback.print_exc()
            return jsonify({
                'error': f'OCR处理失败: {str(e)}'
            }), 500

        finally:
            # OCR处理完成后再删除临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                    print(f"删除临时文件: {temp_file_path}")
                except Exception as e:
                    print(f"删除临时文件失败: {str(e)}")

    except Exception as e:
        print(f"文件处理异常: {str(e)}")
        # 如果出现异常，也要清理临时文件
        if os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                print(f"异常情况下删除临时文件: {temp_file_path}")
            except:
                pass
        return jsonify({'error': f'文件处理失败: {str(e)}'}), 500

@app.route('/info', methods=['GET'])
def get_info():
    """获取服务信息"""
    try:
        device_info = ocr_processor.get_device_info()
        return jsonify({
            'service': 'PaddleOCR Subprocess Service',
            'version': '1.0.0',
            'device': device_info,
            'supported_formats': list(ALLOWED_EXTENSIONS),
            'max_file_size': '1024MB'
        })
    except Exception as e:
        return jsonify({
            'error': f'Failed to get info: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 启动简化版PaddleOCR服务...")
    print("📋 支持的文件格式:", ALLOWED_EXTENSIONS)
    print("🔧 使用subprocess调用paddleocr命令行工具")
    
    # 测试OCR命令可用性
    if ocr_processor.test_ocr_command():
        print("✅ OCR命令测试通过")
        device_info = ocr_processor.get_device_info()
        print(f"🎯 设备信息: {device_info}")
    else:
        print("⚠️  OCR命令测试失败，服务可能无法正常工作")
    
    app.run(host='0.0.0.0', port=9527, debug=True)
