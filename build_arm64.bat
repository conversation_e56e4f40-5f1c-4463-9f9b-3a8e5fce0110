@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 构建ARM64架构PaddleOCR Docker镜像
echo ========================================

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 检查模型文件是否存在
if not exist "paddlex_models.tar.gz" (
    echo ❌ 模型文件 paddlex_models.tar.gz 不存在
    echo 请确保已将PaddleOCR模型文件放置在项目根目录
    pause
    exit /b 1
)

echo ✅ 检测到模型文件: paddlex_models.tar.gz

REM 设置镜像名称和标签
set IMAGE_NAME=paddleocr-service
set IMAGE_TAG=arm64-latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%

echo 🔨 开始构建ARM64镜像...
echo 镜像名称: %FULL_IMAGE_NAME%
echo 使用Dockerfile: Dockerfile.arm64

REM 构建ARM64镜像
docker buildx build --platform linux/arm64 -f Dockerfile.arm64 -t %FULL_IMAGE_NAME% .

if errorlevel 1 (
    echo ❌ ARM64镜像构建失败
    pause
    exit /b 1
)

echo ✅ ARM64镜像构建成功: %FULL_IMAGE_NAME%

REM 显示镜像信息
echo 📋 镜像信息:
docker images | findstr "%IMAGE_NAME%"

echo.
echo 🎉 构建完成！
echo.
echo 📦 导出镜像命令:
echo   docker save %FULL_IMAGE_NAME% -o paddleocr-service-arm64.tar
echo.
echo 🚀 在ARM64设备上加载镜像:
echo   docker load -i paddleocr-service-arm64.tar
echo   docker run -d -p 9527:9527 --name paddleocr-service %FULL_IMAGE_NAME%
echo.

pause
