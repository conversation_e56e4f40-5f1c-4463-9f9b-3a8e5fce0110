@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo =========================================
echo PaddleOCR 项目打包部署脚本 (Windows)
echo =========================================
echo.

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

echo ✓ Docker运行状态正常
echo.

REM 设置变量
set "PROJECT_NAME=paddleocr-docker"
set "PACKAGE_DIR=%PROJECT_NAME%-deployment-package"
set "IMAGE_NAME=paddleocr-ascend"
set "IMAGE_TAG=latest"
set "FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%"

echo 项目打包配置:
echo   项目名称: %PROJECT_NAME%
echo   打包目录: %PACKAGE_DIR%
echo   镜像名称: %FULL_IMAGE_NAME%
echo.

REM 创建打包目录
if exist "%PACKAGE_DIR%" (
    echo 清理旧的打包目录...
    rmdir /s /q "%PACKAGE_DIR%"
)

mkdir "%PACKAGE_DIR%"
echo ✓ 创建打包目录: %PACKAGE_DIR%
echo.

REM 步骤1: 构建Docker镜像
echo 步骤1: 构建ARM64 Docker镜像...
echo.

REM 检查必要文件
set "REQUIRED_FILES=Dockerfile.ascend requirements.ascend.txt app.py ocr_processor.py run.py"
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        echo ❌ 错误: 缺少必要文件: %%f
        pause
        exit /b 1
    )
)

echo ✓ 必要文件检查完成
echo.

REM 构建镜像
echo 开始构建Docker镜像...
docker build --platform linux/arm64 -f Dockerfile.ascend -t "%FULL_IMAGE_NAME%" --progress=plain .

if %errorlevel% neq 0 (
    echo ❌ Docker镜像构建失败
    pause
    exit /b 1
)

echo ✓ Docker镜像构建成功
echo.

REM 步骤2: 导出Docker镜像
echo 步骤2: 导出Docker镜像...
set "IMAGE_FILE=%PACKAGE_DIR%\paddleocr-ascend-arm64.tar"

echo 正在导出镜像到: %IMAGE_FILE%
docker save "%FULL_IMAGE_NAME%" -o "%IMAGE_FILE%"

if %errorlevel% neq 0 (
    echo ❌ 镜像导出失败
    pause
    exit /b 1
)

echo ✓ 镜像导出成功
echo.

REM 步骤3: 复制项目文件
echo 步骤3: 复制项目文件...

REM 复制核心文件
set "CORE_FILES=app.py ocr_processor.py run.py load_env.py .env requirements.txt requirements.ascend.txt"
for %%f in (%CORE_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%PACKAGE_DIR%\" >nul
        echo   复制: %%f
    )
)

REM 复制Docker配置文件
set "DOCKER_FILES=Dockerfile Dockerfile.ascend docker-compose.yml docker-compose.ascend.yml"
for %%f in (%DOCKER_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%PACKAGE_DIR%\" >nul
        echo   复制: %%f
    )
)

REM 复制脚本文件
set "SCRIPT_FILES=run_container.sh build_arm64_server.sh server_setup.sh quick_deploy.sh"
for %%f in (%SCRIPT_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%PACKAGE_DIR%\" >nul
        echo   复制: %%f
    )
)

REM 复制文档文件
set "DOC_FILES=README.md deploy_guide.md troubleshooting.md"
for %%f in (%DOC_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%PACKAGE_DIR%\" >nul
        echo   复制: %%f
    )
)

REM 复制测试文件
if exist "test_image.png" (
    copy "test_image.png" "%PACKAGE_DIR%\" >nul
    echo   复制: test_image.png
)

if exist "test_ocr.py" (
    copy "test_ocr.py" "%PACKAGE_DIR%\" >nul
    echo   复制: test_ocr.py
)

echo ✓ 项目文件复制完成
echo.

REM 步骤4: 创建部署说明
echo 步骤4: 创建部署说明文件...

set "DEPLOY_README=%PACKAGE_DIR%\DEPLOY_README.txt"

echo PaddleOCR 昇腾NPU 部署包 > "%DEPLOY_README%"
echo ================================= >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 本部署包包含以下内容: >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 1. Docker镜像文件: >> "%DEPLOY_README%"
echo    - paddleocr-ascend-arm64.tar (ARM64昇腾NPU版本镜像) >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 2. 项目源代码: >> "%DEPLOY_README%"
echo    - app.py (Flask应用主文件) >> "%DEPLOY_README%"
echo    - ocr_processor.py (OCR处理模块) >> "%DEPLOY_README%"
echo    - run.py (启动脚本) >> "%DEPLOY_README%"
echo    - 其他支持文件 >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 3. 配置文件: >> "%DEPLOY_README%"
echo    - Dockerfile.ascend (昇腾NPU版本Dockerfile) >> "%DEPLOY_README%"
echo    - docker-compose.ascend.yml (Docker Compose配置) >> "%DEPLOY_README%"
echo    - requirements.ascend.txt (昇腾NPU依赖配置) >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 4. 部署脚本: >> "%DEPLOY_README%"
echo    - server_setup.sh (服务器环境配置脚本) >> "%DEPLOY_README%"
echo    - quick_deploy.sh (一键部署脚本) >> "%DEPLOY_README%"
echo    - run_container.sh (容器运行脚本) >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 5. 文档: >> "%DEPLOY_README%"
echo    - deploy_guide.md (详细部署指南) >> "%DEPLOY_README%"
echo    - README.md (项目说明) >> "%DEPLOY_README%"
echo    - troubleshooting.md (故障排除指南) >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 快速部署步骤: >> "%DEPLOY_README%"
echo ============= >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 1. 将整个部署包上传到ARM64昇腾NPU服务器 >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 2. 解压并进入目录: >> "%DEPLOY_README%"
echo    tar -xzf %PROJECT_NAME%-deployment-package.tar.gz >> "%DEPLOY_README%"
echo    cd %PROJECT_NAME%-deployment-package >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 3. 运行一键部署脚本: >> "%DEPLOY_README%"
echo    chmod +x quick_deploy.sh >> "%DEPLOY_README%"
echo    sudo ./quick_deploy.sh >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 4. 验证部署: >> "%DEPLOY_README%"
echo    curl http://localhost:9527/health >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 详细说明请参考 deploy_guide.md 文件。 >> "%DEPLOY_README%"
echo. >> "%DEPLOY_README%"
echo 生成时间: %date% %time% >> "%DEPLOY_README%"

echo ✓ 部署说明文件创建完成
echo.

REM 步骤5: 显示打包结果
echo 步骤5: 打包完成
echo.

echo =========================================
echo ✅ 项目打包完成!
echo =========================================
echo.

echo 打包内容:
dir /b "%PACKAGE_DIR%"
echo.

echo 文件大小统计:
for %%f in ("%PACKAGE_DIR%\*") do (
    echo   %%~nxf: %%~zf bytes
)
echo.

REM 计算总大小
set "TOTAL_SIZE=0"
for /r "%PACKAGE_DIR%" %%f in (*) do (
    set /a "TOTAL_SIZE+=%%~zf"
)

set /a "TOTAL_MB=TOTAL_SIZE/1024/1024"
echo 总大小: %TOTAL_SIZE% bytes (~%TOTAL_MB% MB)
echo.

echo 后续步骤:
echo 1. 可选: 压缩打包目录
echo    tar -czf %PROJECT_NAME%-deployment-package.tar.gz %PACKAGE_DIR%
echo.
echo 2. 将部署包传输到ARM64昇腾NPU服务器
echo    scp -r %PACKAGE_DIR% user@server:/opt/
echo.
echo 3. 在服务器上运行部署脚本
echo    cd /opt/%PACKAGE_DIR%
echo    sudo ./quick_deploy.sh
echo.
echo 4. 详细部署说明请参考:
echo    - %PACKAGE_DIR%\DEPLOY_README.txt
echo    - %PACKAGE_DIR%\deploy_guide.md
echo.

REM 询问是否压缩
set /p "COMPRESS=是否现在压缩打包目录? (y/N): "
if /i "%COMPRESS%"=="y" (
    echo.
    echo 正在压缩打包目录...
    tar -czf "%PROJECT_NAME%-deployment-package.tar.gz" "%PACKAGE_DIR%"
    if !errorlevel! eq 0 (
        echo ✅ 压缩完成: %PROJECT_NAME%-deployment-package.tar.gz
        dir "%PROJECT_NAME%-deployment-package.tar.gz"
    ) else (
        echo ❌ 压缩失败
    )
)

echo.
echo 打包完成! 🎉
pause
