@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo =========================================
echo PaddleOCR 昇腾NPU版本 Docker构建脚本 (修复版)
echo =========================================
echo.

REM 检查Docker是否运行
echo 检查Docker状态...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker Desktop
    echo.
    echo 解决方案:
    echo 1. 启动Docker Desktop
    echo 2. 等待Docker完全启动
    echo 3. 重新运行此脚本
    pause
    exit /b 1
)

echo ✓ Docker运行状态正常
echo.

REM 检查Docker是否支持buildx
echo 检查Docker buildx支持...
docker buildx version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker buildx不可用
    echo.
    echo 解决方案:
    echo 1. 更新Docker Desktop到最新版本
    echo 2. 在Docker Desktop设置中启用实验性功能
    pause
    exit /b 1
)

echo ✓ Docker buildx可用
echo.

REM 检查必要文件
echo 检查必要文件...
set "REQUIRED_FILES=Dockerfile.ascend requirements.ascend.txt app.py ocr_processor.py run.py .env"
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        echo ❌ 错误: 缺少必要文件: %%f
        pause
        exit /b 1
    )
)

echo ✓ 所有必要文件检查完成
echo.

REM 检查昇腾CANN工具包
set "CANN_FILE=Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run"
if not exist "%CANN_FILE%" (
    echo ⚠️  警告: 未找到昇腾CANN工具包文件: %CANN_FILE%
    echo    Docker镜像将不包含CANN工具包，需要在目标服务器上安装
    echo.
    set /p "CONTINUE=是否继续构建? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo 构建已取消
        pause
        exit /b 1
    )
)

REM 清理旧的构建缓存
echo 清理Docker构建缓存...
docker builder prune -f >nul 2>&1

REM 设置镜像信息
set "IMAGE_NAME=paddleocr-arm64-server"
set "IMAGE_TAG=latest"
set "FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%"

echo 开始构建Docker镜像: %FULL_IMAGE_NAME%
echo 构建上下文: %CD%
echo.

REM 显示将要执行的命令
echo 执行构建命令:
echo docker build --platform linux/arm64 -f Dockerfile.ascend -t %FULL_IMAGE_NAME% --no-cache --progress=plain .
echo.

REM 记录开始时间
set "START_TIME=%TIME%"
echo 构建开始时间: %START_TIME%
echo.

REM 构建Docker镜像
docker build --platform linux/arm64 -f Dockerfile.ascend -t %FULL_IMAGE_NAME% --no-cache --progress=plain .

set "BUILD_RESULT=%errorlevel%"
set "END_TIME=%TIME%"

echo.
echo 构建结束时间: %END_TIME%

if %BUILD_RESULT% equ 0 (
    echo.
    echo ✅ Docker镜像构建成功!
    echo 镜像名称: %FULL_IMAGE_NAME%
    echo.
    
    REM 显示镜像信息
    echo 镜像信息:
    docker images %IMAGE_NAME%
    echo.
    
    REM 验证镜像
    echo 验证镜像...
    docker run --rm %FULL_IMAGE_NAME% python -c "import paddle; print('Paddle version:', paddle.__version__)"
    if %errorlevel% equ 0 (
        echo ✅ 镜像验证成功 - Paddle模块可以正常导入
    ) else (
        echo ⚠️  镜像验证失败 - Paddle模块导入有问题
    )
    echo.
    
    echo 后续步骤:
    echo 1. 保存镜像为tar文件:
    echo    docker save %FULL_IMAGE_NAME% -o paddleocr-ascend-arm64.tar
    echo.
    echo 2. 传输到ARM64服务器后加载:
    echo    docker load -i paddleocr-ascend-arm64.tar
    echo.
    echo 3. 在ARM64服务器上运行:
    echo    docker run --name paddleocr-ascend -p 9527:9527 \
    echo      --device=/dev/davinci0:/dev/davinci0 \
    echo      --device=/dev/davinci_manager:/dev/davinci_manager \
    echo      -d %FULL_IMAGE_NAME%
    echo.
    
    REM 询问是否保存镜像
    set /p "SAVE_IMAGE=是否现在保存镜像为tar文件? (y/N): "
    if /i "!SAVE_IMAGE!"=="y" (
        echo.
        echo 正在保存镜像...
        docker save %FULL_IMAGE_NAME% -o paddleocr-ascend-arm64.tar
        if !errorlevel! eq 0 (
            echo ✅ 镜像已保存为: paddleocr-ascend-arm64.tar
            for %%I in (paddleocr-ascend-arm64.tar) do echo 文件大小: %%~zI 字节
        ) else (
            echo ❌ 镜像保存失败
        )
    )
    
) else (
    echo.
    echo ❌ Docker镜像构建失败!
    echo 错误代码: %BUILD_RESULT%
    echo.
    echo 常见问题及解决方案:
    echo.
    echo 1. 网络问题:
    echo    - 检查网络连接
    echo    - 配置Docker代理设置
    echo    - 尝试使用VPN
    echo.
    echo 2. 平台问题:
    echo    - 确保Docker Desktop已启用实验性功能
    echo    - 检查是否支持ARM64模拟
    echo.
    echo 3. 磁盘空间:
    echo    - 检查磁盘剩余空间
    echo    - 清理Docker缓存: docker system prune -a
    echo.
    echo 4. 依赖问题:
    echo    - 检查requirements.ascend.txt
    echo    - 验证PaddlePaddle源是否可访问
    echo.
    echo 如需帮助，请提供完整的错误日志
    pause
    exit /b 1
)

echo.
pause
