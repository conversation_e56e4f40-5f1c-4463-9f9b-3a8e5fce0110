# PaddleOCR 3.1.0 升级指南

## 🎯 升级概述

本指南帮助您将现有的PaddleOCR项目从3.0.0版本升级到3.1.0版本，享受最新的功能和性能改进。

## 🆕 PaddleOCR 3.1.0 新特性

### 核心模型和管道升级

1. **PP-OCRv5多语言文本识别模型**
   - 支持37种语言的训练和推理
   - 包括法语、西班牙语、葡萄牙语、俄语、韩语等
   - 平均准确率提升超过30%

2. **PP-Chart2Table模型升级**
   - 增强图表转表格功能
   - 在内部评估集上，RMS-F1指标提升9.36个百分点（71.24% → 80.60%）

3. **新的文档翻译管道PP-DocTranslation**
   - 基于PP-StructureV3和ERNIE 4.5
   - 支持Markdown格式文档翻译
   - 支持复杂布局PDF文档和文档图像翻译

4. **新的MCP服务器**
   - 支持OCR和PP-StructureV3管道
   - 三种工作模式：本地Python库、AIStudio云服务、自托管服务
   - 支持stdio和HTTP调用

### 性能和稳定性改进

- 优化了模型下载源（从BOS改为HuggingFace）
- 改进了布局分区排序算法
- 增强了模型选择逻辑
- 修复了多个已知问题

### 重要兼容性修复

- **NumPy版本固定为1.26.4**：PaddleOCR 3.1.0默认使用numpy 2.3.2，但与某些依赖不兼容，必须使用1.26.4版本

## 🔄 升级步骤

### 方法1: 自动升级（推荐）

```bash
# 给脚本执行权限
chmod +x upgrade_to_3.1.0.sh

# 运行自动升级脚本
./upgrade_to_3.1.0.sh
```

### 方法2: 手动升级

#### 步骤1: 备份当前环境

```bash
# 备份当前镜像
docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-3.0.0-backup

# 停止当前服务
docker stop paddleocr-service
docker rm paddleocr-service
```

#### 步骤2: 更新依赖版本

依赖版本已自动更新为：
- `paddlepaddle==3.1.0`
- `paddleocr==3.1.0`
- `numpy==1.26.4` （重要：必须使用此版本，不能使用2.x版本）

#### 步骤3: 重新构建镜像

```bash
# 删除旧镜像
docker rmi paddleocr-service:arm64-latest

# 重新构建
docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .
```

#### 步骤4: 重新部署服务

```bash
# 启动新服务
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    paddleocr-service:arm64-latest
```

#### 步骤5: 验证升级

```bash
# 健康检查
curl http://localhost:9527/health

# 运行测试
python3 quick_test.py
```

## 📋 升级前后对比

| 项目 | 3.0.0版本 | 3.1.0版本 | 改进 |
|------|-----------|-----------|------|
| 支持语言 | 中英文为主 | 37种语言 | 大幅扩展 |
| 图表识别 | 基础功能 | 增强版本 | 准确率+9.36% |
| 文档翻译 | 不支持 | PP-DocTranslation | 全新功能 |
| 服务部署 | 基础部署 | MCP服务器 | 多种模式 |
| 模型下载 | BOS源 | HuggingFace | 更稳定 |

## 🔧 兼容性说明

### API兼容性
- ✅ 现有的OCR API完全兼容
- ✅ 健康检查接口保持不变
- ✅ 服务配置参数兼容
- 🆕 新增多语言支持参数

### 模型兼容性
- ✅ 现有预加载模型继续有效
- 🆕 支持更多语言模型
- 🆕 增强的图表识别模型

### 部署兼容性
- ✅ Docker部署方式不变
- ✅ 端口和网络配置兼容
- ✅ 资源要求基本一致

## 🛠️ 故障排除

### 升级失败处理

1. **构建失败**
```bash
# 恢复备份镜像
docker tag paddleocr-service:arm64-3.0.0-backup paddleocr-service:arm64-latest

# 重新启动服务
docker run -d --name paddleocr-service -p 9527:9527 paddleocr-service:arm64-latest
```

2. **服务启动失败**
```bash
# 查看详细日志
docker logs paddleocr-service --tail 50

# 检查资源使用
docker stats paddleocr-service
```

3. **功能异常**
```bash
# 运行完整测试
python3 test_deployment.py

# 检查版本信息
curl http://localhost:9527/
```

### 常见问题

**Q: 升级后识别准确率是否会提升？**
A: 是的，特别是多语言文本识别，平均准确率提升超过30%。

**Q: 升级会影响现有的API调用吗？**
A: 不会，现有API完全兼容，可以无缝升级。

**Q: 升级需要重新准备模型文件吗？**
A: 不需要，现有的`paddlex_models.tar.gz`文件继续有效。

**Q: 升级后镜像大小会增加吗？**
A: 镜像大小基本保持一致，约2-3GB。

## 📊 性能测试

升级后建议进行性能测试：

```bash
# 快速测试
python3 quick_test.py

# 完整功能测试
python3 test_deployment.py

# 性能基准测试
python3 -c "
import time
import requests

# 测试响应时间
start = time.time()
response = requests.get('http://localhost:9527/health')
end = time.time()
print(f'健康检查响应时间: {(end-start)*1000:.2f}ms')

# 测试OCR性能（需要测试图片）
# start = time.time()
# with open('test_image.png', 'rb') as f:
#     response = requests.post('http://localhost:9527/ocr', files={'file': f})
# end = time.time()
# print(f'OCR识别时间: {(end-start)*1000:.2f}ms')
"
```

## 🎉 升级完成

升级完成后，您将享受到：

- 🌍 **37种语言支持** - 更广泛的国际化应用
- 📊 **增强的图表识别** - 更准确的表格转换
- 📄 **文档翻译功能** - 全新的翻译管道
- 🚀 **更好的性能** - 优化的推理速度
- 🔧 **更多部署选项** - MCP服务器支持

## 📞 技术支持

如果在升级过程中遇到问题：

1. 查看升级日志：`docker logs paddleocr-service`
2. 运行诊断脚本：`python3 quick_test.py`
3. 检查系统资源：`docker stats paddleocr-service`
4. 参考故障排除文档：`troubleshooting.md`

---

🎊 **恭喜！您已成功升级到PaddleOCR 3.1.0，享受最新的功能和性能提升！**
