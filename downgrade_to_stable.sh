#!/bin/bash

# 降级到稳定版本脚本
# 将 PaddleOCR 降级到 2.7.3 稳定版本

echo "🔄 PaddleOCR 降级到稳定版本"
echo "=========================="

echo "📋 降级说明:"
echo "  当前版本: PaddleOCR 3.1.0 (存在段错误问题)"
echo "  目标版本: PaddleOCR 2.7.3 (稳定版本)"
echo "  原因: 3.1.0 版本在 ARM64 环境下不稳定"
echo ""

# 询问是否继续
echo "🔄 是否继续降级到稳定版本？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消降级"
    exit 0
fi

# 停止当前容器
if docker ps | grep -q paddleocr-service; then
    echo "🛑 停止当前容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 备份当前镜像
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    echo "💾 备份当前镜像..."
    docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-3.1.0-backup
    echo "✅ 备份完成: paddleocr-service:arm64-3.1.0-backup"
fi

# 清理当前镜像
echo "🧹 清理当前镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 创建稳定版本的 requirements.txt
echo "📝 创建稳定版本依赖文件..."
cat > requirements_stable.txt << 'EOF'
# 稳定版本依赖
flask==3.0.0
werkzeug==3.0.1
numpy==1.24.3
Pillow>=10.1.0
python-dotenv==1.0.0

# PDF处理
PyMuPDF>=1.23.0

# PaddleOCR 稳定版本
paddlepaddle==2.5.2
paddleocr==2.7.3

# 其他依赖
requests>=2.25.1
opencv-python-headless>=4.5.0
shapely>=1.7.1
scikit-image>=0.17.2
imgaug>=0.4.0
pyclipper>=1.2.0
lmdb>=1.2.1
tqdm>=4.61.0
visualdl>=2.2.0
rapidfuzz>=1.3.0
openpyxl>=3.0.7
attrdict>=2.0.1
Polygon3>=3.0.8
lanms-nova>=1.0.2
premailer>=3.10.0
EOF

echo "✅ 稳定版本依赖文件已创建"

# 创建稳定版本的 Dockerfile
echo "📝 创建稳定版本 Dockerfile..."
cat > Dockerfile.stable << 'EOF'
# 稳定版本 Dockerfile - PaddleOCR 2.7.3
FROM python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PADDLE_DISABLE_CUSTOM_DEVICE=1

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgfortran5 \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制稳定版本依赖文件
COPY requirements_stable.txt requirements.txt

# 设置pip源
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple && \
    pip config set global.trusted-host mirrors.tuna.tsinghua.edu.cn

# 升级pip
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制模型文件并预加载到传统路径
COPY paddlex_models.tar.gz /tmp/
RUN mkdir -p /root/.paddleocr && \
    cd /tmp && \
    tar -xzf paddlex_models.tar.gz && \
    # PaddleOCR 2.7.3 使用传统路径
    if [ -d ".paddlex/official_models" ]; then \
        cp -r .paddlex/official_models/* /root/.paddleocr/ && \
        echo "✅ 从 .paddlex/official_models/ 复制模型文件"; \
    else \
        cp -r .paddlex/* /root/.paddleocr/ && \
        echo "✅ 从 .paddlex/ 复制所有文件"; \
    fi && \
    rm -rf /tmp/paddlex_models.tar.gz /tmp/.paddlex && \
    echo "✅ PaddleOCR 2.7.3 模型预加载完成" && \
    echo "📦 模型目录内容:" && \
    ls -la /root/.paddleocr/ && \
    echo "📦 模型目录大小:" && \
    du -sh /root/.paddleocr/

# 复制应用代码
COPY app.py .
COPY ocr_processor.py .
COPY load_env.py .
COPY run.py .
COPY .env* ./

# 创建日志目录
RUN mkdir -p /app/logs

# 验证安装
RUN python -c "import paddle; print('PaddlePaddle version:', paddle.__version__)" && \
    python -c "import paddleocr; print('PaddleOCR version: 2.7.3')" && \
    pip list | grep paddle

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:9527/health || exit 1

# 暴露端口
EXPOSE 9527

# 启动应用
CMD ["python", "run.py"]
EOF

echo "✅ 稳定版本 Dockerfile 已创建"

# 构建稳定版本镜像
echo ""
echo "🔨 构建稳定版本镜像..."
start_time=$(date +%s)

if docker build -f Dockerfile.stable -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 稳定版本镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 稳定版本镜像构建失败"
    exit 1
fi

# 重新部署服务
echo ""
echo "🚀 部署稳定版本服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    --shm-size 512m \
    -e OMP_NUM_THREADS=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 稳定版本服务部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..10}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/10)"
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 健康检查失败"
        docker logs paddleocr-service --tail 20
        exit 1
    fi
done

echo ""
echo "🎉 降级到稳定版本完成！"
echo "======================="
echo "✅ PaddleOCR 2.7.3 稳定版本部署成功"
echo "✅ 应该解决了段错误问题"
echo ""
echo "📋 版本信息:"
echo "   PaddleOCR: 2.7.3"
echo "   PaddlePaddle: 2.5.2"
echo "   NumPy: 1.24.3"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   docker exec paddleocr-service paddleocr ocr -i /app/test.png --device cpu"
echo ""
echo "💾 备份信息:"
echo "   3.1.0版本备份: paddleocr-service:arm64-3.1.0-backup"
echo "   删除备份: docker rmi paddleocr-service:arm64-3.1.0-backup"
echo ""

# 清理临时文件
rm -f requirements_stable.txt Dockerfile.stable

echo "✅ 降级流程完成！"
