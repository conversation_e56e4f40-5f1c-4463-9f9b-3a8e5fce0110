FROM ubuntu:22.04

WORKDIR /app

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 更新软件源为阿里云镜像（ARM64优化）
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*ports.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3 python3-pip python3-dev \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    wget \
    curl \
    unzip \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# 注意：昇腾环境依赖宿主机安装，容器启动时需要挂载宿主机的昇腾目录
# 这里只安装必要的开发工具包，运行时库将从宿主机挂载

# 可选：安装容器内的CANN工具包（如果宿主机没有完整安装）
# 使用用户指定的CANN 8.0.RC1版本，与paddle-custom-npu 3.1.0兼容
# 注意：用户需要自行下载对应的CANN安装包
COPY Ascend-cann-toolkit_24.1.rc1_linux-aarch64.run /tmp/
RUN echo "安装昇腾CANN工具包24.1.rc1（与驱动版本匹配）..." && \
    chmod +x /tmp/Ascend-cann-toolkit_24.1.rc1_linux-aarch64.run && \
    /tmp/Ascend-cann-toolkit_24.1.rc1_linux-aarch64.run --install --quiet && \
    echo "CANN 24.1.rc1工具包安装完成" && \
    rm -f /tmp/Ascend-cann-toolkit_24.1.rc1_linux-aarch64.run

# 设置昇腾环境变量（同时支持容器内和宿主机挂载的路径）
ENV LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64:/usr/local/Ascend/ascend-toolkit/latest/lib64/plugin/opskernel:/usr/local/Ascend/ascend-toolkit/latest/lib64/plugin/nnengine:/usr/local/Ascend/ascend-toolkit/latest/atc/lib64:/usr/local/Ascend/driver/lib64:/usr/local/Ascend/add-ons:$LD_LIBRARY_PATH
ENV PATH=/usr/local/Ascend/ascend-toolkit/latest/bin:/usr/local/Ascend/ascend-toolkit/latest/atc/bin:$PATH
ENV ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp
ENV ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest
ENV ASCEND_TOOLKIT_HOME=/usr/local/Ascend/ascend-toolkit/latest

# 验证昇腾安装
#RUN echo "验证昇腾CANN安装..." && \
#    echo "检查Ascend目录结构:" && \
#    find /usr/local/Ascend -name "libatb.so*" 2>/dev/null || echo "未找到libatb.so" && \
#    echo "检查所有lib64目录:" && \
#    find /usr/local/Ascend -name "lib64" -type d 2>/dev/null && \
#    echo "检查关键库文件:" && \
#    find /usr/local/Ascend -name "*.so" | grep -E "(libatb|libacl|libge)" | head -10 && \
#    echo "当前LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

COPY requirements.ascend.txt .
COPY app.py .
COPY ocr_processor.py .
COPY load_env.py .
COPY run.py .
COPY .env .

# 配置pip源和信任主机
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip config set install.trusted-host www.paddlepaddle.org.cn

# 升级pip和基础工具
RUN pip install --upgrade pip setuptools wheel

# 显示pip配置
RUN pip config list

# 安装基础依赖
RUN pip install --no-cache-dir flask==3.0.0 werkzeug==3.0.1 "numpy>=1.26.0" "Pillow>=10.1.0" python-dotenv==1.0.0

# 安装PDF处理依赖
RUN pip install --no-cache-dir "PyMuPDF>=1.23.0"

# 安装用户指定的版本组合
# paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0 + paddleocr 3.0.0
# 使用最新版本组合，配合CANN 8.0.RC1

# 先安装基础PaddlePaddle 3.1.0（用户指定版本）
RUN echo "开始安装基础PaddlePaddle 3.1.0..." && \
    pip install --no-cache-dir paddlepaddle==3.1.0 \
    -i https://www.paddlepaddle.org.cn/packages/stable/cpu/ \
    --trusted-host www.paddlepaddle.org.cn \
    --timeout 600 && \
    echo "PaddlePaddle 3.1.0安装完成"

# 安装paddle-custom-npu 3.1.0（用户指定版本）
RUN echo "开始安装paddle-custom-npu 3.1.0..." && \
    pip install --no-cache-dir paddle-custom-npu==3.1.0 \
    -i https://www.paddlepaddle.org.cn/packages/stable/npu/ \
    --trusted-host www.paddlepaddle.org.cn \
    --timeout 600 && \
    echo "paddle-custom-npu 3.1.0安装完成"

# 安装PaddleOCR 3.0.0（与paddle 3.1.0版本匹配）
RUN echo "开始安装paddleocr 3.0.0（最新版本）..." && \
    pip install --no-cache-dir paddleocr==3.0.0 \
    -i https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com \
    --timeout 600 && \
    echo "paddleocr 3.0.0安装完成"

# 验证安装的包
RUN echo "验证已安装的paddle相关包:" && \
    pip list | grep -i paddle

# 检查安装的模块结构
RUN echo "检查paddle相关模块..." && \
    python -c "import sys; import pkgutil; print('已安装的paddle相关模块:'); [print(f'  {name}') for finder, name, ispkg in pkgutil.iter_modules() if 'paddle' in name.lower()]"

# 验证paddle模块可以正常导入（构建时跳过NPU初始化）
RUN echo "测试paddle模块导入（构建阶段）..." && \
    echo "注意：构建时跳过NPU设备初始化，运行时将自动检测NPU" && \
    echo "使用用户指定版本组合：paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0 + paddleocr 3.0.0" && \
    CUSTOM_DEVICE_ROOT="" FLAGS_init_allocated_mem=false python -c "import paddle; print('✅ Paddle version:', paddle.__version__); print('✅ Paddle基础模块导入成功'); print('✅ NPU支持将在容器运行时启用')" && \
    echo "paddle模块导入测试通过（构建阶段）"

# 验证paddleocr模块可以正常导入
RUN echo "测试paddleocr模块导入..." && \
    echo "使用最新版本：paddleocr 3.0.0（与paddle 3.1.0 + custom-npu 3.1.0匹配）" && \
    python -c "import paddleocr; print('✅ PaddleOCR 3.0.0 imported successfully')" && \
    echo "paddleocr 3.0.0模块导入测试通过"

RUN mkdir -p /tmp/paddle_cache

RUN chmod -R 755 /app

EXPOSE 9527

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9527/health || exit 1

CMD ["python", "run.py"]
