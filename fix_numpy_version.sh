#!/bin/bash

# NumPy版本修复脚本
# 修复numpy版本兼容性问题，确保使用1.26.4版本

echo "🔧 NumPy版本修复脚本"
echo "===================="

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 显示当前numpy版本要求
echo "📋 版本修复信息:"
echo "  问题: PaddleOCR 3.1.0默认使用numpy 2.3.2"
echo "  要求: 必须使用numpy 1.26.4"
echo "  原因: numpy 2.x版本与某些依赖不兼容"
echo ""

# 检查requirements.txt中的numpy版本
if grep -q "numpy==1.26.4" requirements.txt; then
    echo "✅ requirements.txt中numpy版本已正确设置为1.26.4"
else
    echo "❌ requirements.txt中numpy版本需要修复"
    exit 1
fi

# 询问是否继续
echo "🔄 是否重新构建镜像以修复numpy版本？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消操作"
    exit 0
fi

# 停止旧服务
echo "🛑 停止旧服务..."
docker stop paddleocr-service 2>/dev/null || true
docker rm paddleocr-service 2>/dev/null || true

# 删除旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 重新构建镜像
echo ""
echo "🔨 重新构建镜像（使用numpy 1.26.4）..."
echo "这可能需要 10-30 分钟，请耐心等待..."

start_time=$(date +%s)

if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 验证numpy版本
echo ""
echo "🔍 验证numpy版本..."
numpy_version=$(docker run --rm paddleocr-service:arm64-latest python -c "import numpy; print(numpy.__version__)" 2>/dev/null)
if [ "$numpy_version" = "1.26.4" ]; then
    echo "✅ numpy版本验证成功: $numpy_version"
else
    echo "❌ numpy版本验证失败: $numpy_version (期望: 1.26.4)"
    exit 1
fi

# 重新部署服务
echo ""
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 20
    exit 1
fi

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..12}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/12)"
        sleep 5
    fi
    
    if [ $i -eq 12 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 20
        exit 1
    fi
done

# 验证PaddleOCR功能
echo ""
echo "🧪 验证PaddleOCR功能..."
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ PaddleOCR服务正常')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
    else:
        print('❌ 服务响应异常')
except Exception as e:
    print(f'❌ 验证失败: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法验证服务信息，但服务可能仍然正常"
fi

# 显示完成信息
echo ""
echo "🎉 NumPy版本修复完成！"
echo "======================"
echo "✅ numpy版本已修复为1.26.4"
echo "✅ PaddleOCR服务正常运行"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   python3 test_deployment.py"
echo ""
echo "🔍 版本验证命令:"
echo "   docker exec paddleocr-service python -c \"import numpy; print('numpy:', numpy.__version__)\""
echo "   docker exec paddleocr-service python -c \"import paddleocr; print('paddleocr: 3.1.0')\""
echo ""

echo "✅ 修复流程完成！"
