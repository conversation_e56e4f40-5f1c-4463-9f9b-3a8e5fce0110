#!/bin/bash

# ARM64昇腾NPU服务器环境配置脚本
# 用于配置昇腾NPU服务器的基础环境

set -e

echo "========================================="
echo "ARM64昇腾NPU服务器环境配置脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 此脚本只能在ARM64 (aarch64) 系统上运行"
    exit 1
fi

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 错误: 请使用root权限运行此脚本"
    echo "使用命令: sudo $0"
    exit 1
fi

echo "✓ 权限检查通过"
echo

# 更新系统包
echo "更新系统包..."
apt-get update
apt-get upgrade -y

# 安装基础依赖
echo "安装基础依赖..."
apt-get install -y \
    curl \
    wget \
    unzip \
    tar \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    software-properties-common

echo "✓ 基础依赖安装完成"
echo

# 安装Docker
echo "检查Docker安装状态..."
if ! command -v docker &> /dev/null; then
    echo "安装Docker..."
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=arm64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新包索引并安装Docker
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    echo "✓ Docker安装完成"
else
    echo "✓ Docker已安装"
fi

# 检查Docker运行状态
if ! docker info >/dev/null 2>&1; then
    echo "启动Docker服务..."
    systemctl start docker
fi

echo "✓ Docker运行正常"
echo

# 安装Docker Compose (如果需要)
echo "检查Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    echo "安装Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-linux-aarch64" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo "✓ Docker Compose安装完成"
else
    echo "✓ Docker Compose已安装"
fi

# 检查昇腾设备
echo "检查昇腾NPU设备..."

# 检测所有可能的昇腾设备
FOUND_DEVICES=""
for i in {0..7}; do
    if [ -e "/dev/davinci$i" ]; then
        echo "✓ 检测到昇腾NPU设备: /dev/davinci$i"
        FOUND_DEVICES="$FOUND_DEVICES davinci$i"
    fi
done

if [ -n "$FOUND_DEVICES" ]; then
    echo "✓ 发现昇腾NPU设备: $FOUND_DEVICES"

    # 检查昇腾驱动和工具包
    if [ -d "/usr/local/Ascend" ]; then
        echo "✓ 检测到昇腾软件栈"

        # 显示昇腾设备信息
        if [ -f "/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi" ]; then
            echo "昇腾设备信息:"
            /usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info || echo "无法获取设备信息"
        fi

        # 检查设备权限
        echo "检查设备权限:"
        for device in $FOUND_DEVICES; do
            ls -la /dev/$device
        done

    else
        echo "⚠️  警告: 未检测到昇腾软件栈"
        echo "请手动安装昇腾CANN工具包:"
        echo "1. 下载CANN工具包: https://www.hiascend.com/software/cann"
        echo "2. 安装命令: chmod +x Ascend-cann-toolkit_*.run && ./Ascend-cann-toolkit_*.run --install"
    fi
else
    echo "⚠️  警告: 未检测到昇腾NPU设备"
    echo "请检查:"
    echo "1. 昇腾NPU驱动是否正确安装"
    echo "2. 设备是否正确连接"
    echo "3. 系统是否支持昇腾NPU"
    echo "4. 设备文件是否存在: ls -la /dev/davinci*"
fi
echo

# 配置系统参数
echo "配置系统参数..."

# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 配置内核参数
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
sysctl -p

echo "✓ 系统参数配置完成"
echo

# 创建项目目录
PROJECT_DIR="/opt/paddleocr-docker"
echo "创建项目目录: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
chown -R 1000:1000 "$PROJECT_DIR"

echo "✓ 项目目录创建完成"
echo

# 配置防火墙（如果启用）
if systemctl is-active --quiet ufw; then
    echo "配置防火墙规则..."
    ufw allow 9527/tcp
    echo "✓ 防火墙规则配置完成"
fi

echo "========================================="
echo "✅ 服务器环境配置完成!"
echo "========================================="
echo
echo "配置摘要:"
echo "- 系统架构: $ARCH"
echo "- Docker版本: $(docker --version)"
echo "- Docker Compose版本: $(docker-compose --version)"
echo "- 项目目录: $PROJECT_DIR"
echo "- 服务端口: 9527"
echo
echo "后续步骤:"
echo "1. 将项目文件上传到: $PROJECT_DIR"
echo "2. 进入项目目录: cd $PROJECT_DIR"
echo "3. 构建Docker镜像: ./build_arm64_server.sh"
echo "4. 运行服务: ./run_container.sh"
echo "5. 测试服务: curl http://localhost:9527/health"
echo
echo "注意事项:"
echo "- 如果使用昇腾NPU，请确保已安装CANN工具包"
echo "- 首次运行时PaddleOCR会下载模型文件，需要网络连接"
echo "- 建议配置至少8GB内存和50GB磁盘空间"
