#!/bin/bash

# 昇腾NPU设备检测脚本
# 用于检测和验证昇腾NPU设备配置

echo "========================================="
echo "昇腾NPU设备检测脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "⚠️  警告: 当前系统不是ARM64架构"
fi
echo

# 检测昇腾设备文件
echo "检测昇腾设备文件..."
FOUND_DEVICES=""
for i in {0..7}; do
    if [ -e "/dev/davinci$i" ]; then
        echo "✓ 发现设备: /dev/davinci$i"
        ls -la /dev/davinci$i
        FOUND_DEVICES="$FOUND_DEVICES $i"
    fi
done

if [ -z "$FOUND_DEVICES" ]; then
    echo "❌ 未发现任何昇腾设备文件"
    echo "请检查昇腾驱动是否正确安装"
else
    echo "✓ 发现昇腾设备ID: $FOUND_DEVICES"
fi
echo

# 检测管理设备
echo "检测昇腾管理设备..."
MGMT_DEVICES="davinci_manager devmm_svm hisi_hdc"
for device in $MGMT_DEVICES; do
    if [ -e "/dev/$device" ]; then
        echo "✓ 发现管理设备: /dev/$device"
        ls -la /dev/$device
    else
        echo "❌ 缺少管理设备: /dev/$device"
    fi
done
echo

# 检查昇腾软件栈
echo "检查昇腾软件栈..."
if [ -d "/usr/local/Ascend" ]; then
    echo "✓ 检测到昇腾软件栈: /usr/local/Ascend"
    
    # 检查CANN工具包版本
    if [ -f "/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi" ]; then
        echo "✓ 发现npu-smi工具"
        echo "CANN工具包版本信息:"
        /usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info 2>/dev/null || echo "无法获取版本信息"
    else
        echo "❌ 未找到npu-smi工具"
    fi
    
    # 检查库文件
    if [ -d "/usr/local/Ascend/ascend-toolkit/latest/lib64" ]; then
        echo "✓ 发现库文件目录"
        echo "库文件数量: $(ls /usr/local/Ascend/ascend-toolkit/latest/lib64/ | wc -l)"
    else
        echo "❌ 未找到库文件目录"
    fi
    
else
    echo "❌ 未检测到昇腾软件栈"
    echo "请安装CANN工具包"
fi
echo

# 检查环境变量
echo "检查昇腾环境变量..."
ENV_VARS="LD_LIBRARY_PATH PATH ASCEND_OPP_PATH ASCEND_AICPU_PATH"
for var in $ENV_VARS; do
    if [ -n "${!var}" ]; then
        echo "✓ $var = ${!var}"
    else
        echo "⚠️  $var 未设置"
    fi
done
echo

# 检查驱动模块
echo "检查昇腾驱动模块..."
if lsmod | grep -q "drv_davinci"; then
    echo "✓ 昇腾驱动模块已加载"
    lsmod | grep drv_davinci
else
    echo "❌ 昇腾驱动模块未加载"
fi
echo

# 生成Docker配置建议
if [ -n "$FOUND_DEVICES" ]; then
    echo "========================================="
    echo "Docker配置建议"
    echo "========================================="
    echo
    
    echo "根据检测到的设备，建议的Docker运行命令:"
    echo
    echo "docker run --name paddleocr-ascend -p 9527:9527 \\"
    
    # 添加设备挂载
    for device_id in $FOUND_DEVICES; do
        echo "  --device=/dev/davinci$device_id:/dev/davinci$device_id \\"
    done
    
    # 添加管理设备
    for device in $MGMT_DEVICES; do
        if [ -e "/dev/$device" ]; then
            echo "  --device=/dev/$device:/dev/$device \\"
        fi
    done
    
    # 添加软件栈挂载
    if [ -d "/usr/local/Ascend" ]; then
        echo "  -v /usr/local/Ascend:/usr/local/Ascend:ro \\"
    fi
    
    # 添加环境变量
    FIRST_DEVICE=$(echo $FOUND_DEVICES | awk '{print $1}')
    echo "  -e FLAGS_selected_npus=$FIRST_DEVICE \\"
    echo "  -e ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp \\"
    echo "  -e ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest \\"
    echo "  -e LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64 \\"
    echo "  --restart=unless-stopped \\"
    echo "  -d paddleocr-ascend:latest"
    echo
    
    echo "建议的docker-compose.ascend.yml设备配置:"
    echo "devices:"
    for device_id in $FOUND_DEVICES; do
        echo "  - /dev/davinci$device_id:/dev/davinci$device_id"
    done
    for device in $MGMT_DEVICES; do
        if [ -e "/dev/$device" ]; then
            echo "  - /dev/$device:/dev/$device"
        fi
    done
    echo
    echo "建议的环境变量:"
    echo "environment:"
    echo "  - FLAGS_selected_npus=$FIRST_DEVICE"
    echo
fi

# 生成配置文件更新建议
echo "========================================="
echo "配置文件更新建议"
echo "========================================="
echo

if [ -n "$FOUND_DEVICES" ]; then
    FIRST_DEVICE=$(echo $FOUND_DEVICES | awk '{print $1}')
    echo "请更新以下配置文件中的设备ID:"
    echo
    echo "1. docker-compose.ascend.yml:"
    echo "   将 FLAGS_selected_npus 设置为: $FIRST_DEVICE"
    echo
    echo "2. 如果使用手动Docker命令，设置:"
    echo "   -e FLAGS_selected_npus=$FIRST_DEVICE"
    echo
    echo "3. 确保挂载所有检测到的设备:"
    for device_id in $FOUND_DEVICES; do
        echo "   --device=/dev/davinci$device_id:/dev/davinci$device_id"
    done
fi

echo
echo "检测完成!"
echo "如果发现问题，请参考troubleshooting.md文档"
