#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
官方容器内的服务启动脚本
用于在官方PaddleOCR容器内启动OCR服务
"""

import os
import sys
import time
import subprocess
import traceback

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'flask',
        'werkzeug',
        'paddlepaddle',
        'paddleocr'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    print("📦 安装依赖...")
    
    # 基础依赖
    basic_packages = [
        'flask==3.0.0',
        'werkzeug==3.0.1'
    ]
    
    # 安装基础依赖
    for package in basic_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                package, '--no-cache-dir'
            ])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    # 检查PaddlePaddle和PaddleOCR
    try:
        import paddle
        print("✅ PaddlePaddle 已存在")
    except ImportError:
        print("📦 安装PaddlePaddle...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                'paddlepaddle', '--no-cache-dir'
            ])
            print("✅ PaddlePaddle 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ PaddlePaddle 安装失败: {e}")
            return False
    
    try:
        import paddleocr
        print("✅ PaddleOCR 已存在")
    except ImportError:
        print("📦 安装PaddleOCR...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                'paddleocr==3.0.0', '--no-cache-dir'
            ])
            print("✅ PaddleOCR 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ PaddleOCR 安装失败: {e}")
            return False
    
    return True

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 设置工作目录
    work_dir = '/work'
    if not os.path.exists(work_dir):
        os.makedirs(work_dir)
        print(f"✅ 创建工作目录: {work_dir}")
    
    os.chdir(work_dir)
    print(f"✅ 切换到工作目录: {work_dir}")
    
    # 设置Python路径
    if work_dir not in sys.path:
        sys.path.insert(0, work_dir)
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = work_dir + ':' + os.environ.get('PYTHONPATH', '')
    os.environ['FLASK_APP'] = 'ocr_service.py'
    os.environ['FLASK_ENV'] = 'production'
    
    print("✅ 环境设置完成")

def copy_service_files():
    """复制服务文件到工作目录"""
    print("📁 准备服务文件...")
    
    work_dir = '/work'
    
    # 服务文件内容（如果文件不存在则创建）
    service_files = {
        'ocr_service.py': '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
官方容器内的OCR服务
直接在官方PaddleOCR容器内运行的Flask服务
"""

import os
import tempfile
import traceback
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from ocr_utils import OCRProcessor

# 创建Flask应用
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 限制上传文件大小为1024MB
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()  # 使用系统临时目录

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg'}

# 全局OCR处理器
ocr_processor = None

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        global ocr_processor
        if ocr_processor and ocr_processor.is_ready():
            return jsonify({
                'status': 'ok',
                'mode': 'official_container_direct',
                'ocr_ready': True,
                'device': ocr_processor.get_device_info()
            })
        else:
            return jsonify({
                'status': 'initializing',
                'mode': 'official_container_direct',
                'ocr_ready': False,
                'message': 'OCR正在初始化中...'
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/ocr', methods=['POST'])
def ocr_recognition():
    """OCR识别接口"""
    print("🔥 收到OCR请求")
    
    global ocr_processor
    if not ocr_processor or not ocr_processor.is_ready():
        return jsonify({
            'error': 'OCR服务未就绪，请稍后重试'
        }), 503

    # 检查是否有文件上传
    if 'file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': f'不支持的文件类型，仅支持 {", ".join(ALLOWED_EXTENSIONS)}'}), 400

    file_path = None
    try:
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # OCR识别
        result = ocr_processor.process(file_path)

        if result is None or len(result) == 0:
            return jsonify({
                'texts': [],
                'warning': '未检测到文本内容'
            })

        if isinstance(result, dict) and "error" in result:
            return jsonify({'error': result["error"]})

        return jsonify({
            'texts': result,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'OCR处理失败: {str(e)}'
        }), 500
    finally:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except:
                pass

def initialize_ocr():
    """初始化OCR处理器"""
    global ocr_processor
    try:
        print("🚀 初始化OCR处理器...")
        ocr_processor = OCRProcessor()
        print("✅ OCR处理器初始化完成")
        return True
    except Exception as e:
        print(f"❌ OCR处理器初始化失败: {e}")
        return False

if __name__ == '__main__':
    if not initialize_ocr():
        exit(1)
    app.run(host='0.0.0.0', port=9527, debug=False, threaded=True)
''',
        
        'ocr_utils.py': '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import traceback

class OCRProcessor:
    def __init__(self):
        self.ocr = None
        self.paddle = None
        self.device_info = "未知"
        self.ready = False
        self._init_ocr()
    
    def _init_ocr(self):
        try:
            import paddle
            from paddleocr import PaddleOCR
            
            self.paddle = paddle
            self.device_info = self._detect_device()
            self._setup_environment()
            
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            self.ready = True
            print("✅ PaddleOCR初始化成功")
            
        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            self.ready = False
            raise e
    
    def _detect_device(self):
        try:
            if self.paddle:
                try:
                    available_devices = self.paddle.device.get_all_custom_device_type()
                    if 'npu' in available_devices:
                        return "NPU"
                except:
                    pass
                
                try:
                    if self.paddle.device.is_compiled_with_cuda():
                        gpu_count = self.paddle.device.cuda.device_count()
                        if gpu_count > 0:
                            return f"GPU (数量: {gpu_count})"
                except:
                    pass
            
            return "CPU"
        except:
            return "CPU"
    
    def _setup_environment(self):
        try:
            os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
            if 'npu' in self.device_info.lower():
                os.environ['FLAGS_enable_eager_mode'] = '1'
                os.environ['ASCEND_RT_VISIBLE_DEVICES'] = '0,1,2,3,4,5,6,7'
        except:
            pass
    
    def is_ready(self):
        return self.ready and self.ocr is not None
    
    def get_device_info(self):
        return self.device_info
    
    def process(self, file_path):
        if not self.is_ready():
            return {"error": "OCR服务未就绪"}
        
        try:
            if not os.path.exists(file_path):
                return {"error": f"文件不存在: {file_path}"}
            
            result = self.ocr.ocr(file_path)
            
            if not result:
                return []
            
            text_results = []
            for page_result in result:
                if page_result:
                    page_texts = []
                    for line in page_result:
                        if len(line) >= 2 and len(line[1]) >= 1:
                            text = line[1][0]
                            confidence = line[1][1] if len(line[1]) >= 2 else 0.0
                            if confidence > 0.5:
                                page_texts.append(text)
                    text_results.append(page_texts)
                else:
                    text_results.append([])
            
            return text_results
            
        except Exception as e:
            return {"error": f"OCR处理异常: {str(e)}"}
'''
    }
    
    # 写入文件
    for filename, content in service_files.items():
        file_path = os.path.join(work_dir, filename)
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 创建文件: {filename}")
        else:
            print(f"✅ 文件已存在: {filename}")

def start_service():
    """启动OCR服务"""
    print("🚀 启动OCR服务...")
    
    try:
        # 导入并启动服务
        from ocr_service import app, initialize_ocr
        
        # 初始化OCR
        if not initialize_ocr():
            print("❌ OCR初始化失败")
            return False
        
        # 启动Flask服务
        print("🌐 启动Flask服务在端口9527...")
        app.run(
            host='0.0.0.0',
            port=9527,
            debug=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        print(f"异常详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🚀 PaddleOCR官方容器服务启动器")
    print("=" * 50)
    
    try:
        # 1. 设置环境
        setup_environment()
        
        # 2. 检查依赖
        missing = check_dependencies()
        if missing:
            if not install_dependencies():
                print("❌ 依赖安装失败")
                return False
        
        # 3. 准备服务文件
        copy_service_files()
        
        # 4. 启动服务
        start_service()
        
    except KeyboardInterrupt:
        print("\\n⚠️ 服务被用户中断")
    except Exception as e:
        print(f"\\n❌ 启动失败: {e}")
        print(f"异常详情: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    main()
