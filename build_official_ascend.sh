#!/bin/bash

# 官方昇腾镜像版本Docker构建脚本
# 使用官方镜像：ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84
# 适用于ARM64架构的昇腾NPU服务器

set -e

echo "========================================="
echo "PaddleOCR 官方昇腾镜像版本 Docker构建脚本"
echo "使用官方镜像和新版本组合"
echo "========================================="
echo

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    exit 1
fi

# 检查架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "⚠️  警告: 当前系统不是ARM64架构，构建的镜像可能无法在昇腾NPU服务器上运行"
    read -p "是否继续构建? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "构建已取消"
        exit 1
    fi
fi

# 检查必要文件
REQUIRED_FILES=("Dockerfile.ascend" "requirements.ascend.txt" "app.py" "ocr_processor.py" "run.py")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必要文件: $file"
        exit 1
    fi
done

echo "✓ 所有必要文件检查完成"
echo

# 显示版本信息
echo "📦 使用的版本组合:"
echo "   - 基础镜像: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84"
echo "   - PaddlePaddle: 3.1.0"
echo "   - paddle-custom-npu: 3.1.0"
echo "   - PaddleOCR: 3.1.0"
echo "   - NumPy: 1.26.4"
echo "   - CANN: 8.0.0 (官方镜像内置)"
echo

# 设置镜像标签
IMAGE_NAME="paddleocr-official-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "开始构建Docker镜像: $FULL_IMAGE_NAME"
echo "构建上下文: $(pwd)"
echo

# 构建Docker镜像
echo "执行构建命令..."
echo "docker build --platform linux/arm64 -f Dockerfile.ascend -t $FULL_IMAGE_NAME --no-cache --progress=plain ."
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

docker build \
    --platform linux/arm64 \
    -f Dockerfile.ascend \
    -t "$FULL_IMAGE_NAME" \
    --no-cache \
    --progress=plain \
    .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ Docker镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    # 显示镜像大小
    IMAGE_SIZE=$(docker images --format "table {{.Size}}" "$FULL_IMAGE_NAME" | tail -n 1)
    echo "镜像大小: $IMAGE_SIZE"
    echo
    
    echo "🚀 后续步骤:"
    echo
    echo "1. 快速启动容器 (推荐):"
    echo "   ./run_official_ascend.sh"
    echo
    echo "2. 自动化部署:"
    echo "   ./deploy_arm_server.sh"
    echo
    echo "3. 手动启动容器 (官方推荐方式 - NPU模式):"
    echo "   docker run -d --name paddleocr-official-ascend \\"
    echo "     --restart unless-stopped --privileged --network=host --shm-size=128G \\"
    echo "     -v \$(pwd):/work -w=/work \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo "   注意: NPU模式使用host网络，挂载完整Ascend目录确保库文件完整"
    echo
    echo "2. 测试服务健康状态:"
    echo "   curl http://localhost:9527/health"
    echo
    echo "3. 测试OCR功能:"
    echo "   curl -X POST -F 'file=@test_image.png' http://localhost:9527/ocr"
    echo
    echo "4. 查看容器日志:"
    echo "   docker logs paddleocr-official-ascend"
    echo
    echo "5. 进入容器调试:"
    echo "   docker exec -it paddleocr-official-ascend bash"
    echo
    echo "📝 注意事项:"
    echo "   - 确保ARM服务器已安装昇腾驱动"
    echo "   - 确保昇腾环境组件存在 (/usr/local/Ascend/driver, /usr/local/bin/npu-smi)"
    echo "   - 官方镜像已包含CANN 8.0.0环境"
    echo "   - 使用NPU加速需要正确的环境挂载"
    
else
    echo
    echo "❌ Docker镜像构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo "请检查构建日志中的错误信息"
    echo
    echo "🔧 常见问题排查:"
    echo "1. 检查网络连接是否正常"
    echo "2. 检查官方镜像是否可以正常拉取"
    echo "3. 检查磁盘空间是否充足"
    echo "4. 检查Docker版本是否支持ARM64构建"
    exit 1
fi
