# 昇腾环境完整安装指南

本文档说明如何完整安装昇腾环境，包括驱动、固件和CANN 8.0.RC1工具包，以配合paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0使用。

## ⚠️ 重要提醒

**昇腾NPU需要在宿主机上安装驱动和固件，这是使用昇腾NPU的前提条件！**

## 安装顺序

昇腾环境必须按以下顺序安装：

```
1. 昇腾驱动 (Driver) - 宿主机必须安装 ⭐
2. 昇腾固件 (Firmware) - 宿主机必须安装 ⭐
3. CANN工具包 (Toolkit) - 可以在宿主机或容器中安装
```

## 第一步：昇腾驱动安装

### 1. 驱动包下载
从华为昇腾官网下载以下文件：
- **驱动包**: `Ascend-hdk-910b-npu-driver_xxx_linux-aarch64.run`
- **固件包**: `Ascend-hdk-910b-npu-firmware_xxx_linux-aarch64.run`

### 2. 驱动安装前准备

**安装必要的系统依赖：**

```bash
# 1. 更新系统包
sudo apt update

# 2. 安装内核开发包和编译工具
sudo apt install -y linux-headers-$(uname -r) linux-headers-generic
sudo apt install -y build-essential dkms gcc make

# 3. 检查内核版本和开发包
uname -r
ls /lib/modules/$(uname -r)/build
```

### 3. 驱动安装步骤

**如果遇到权限错误 `ERR_NO:0x0091`，请先执行权限修复：**

```bash
# 0. 修复权限配置（如果遇到权限错误）
sudo rm -f /etc/ascend_install.info
echo 'install_username=root' | sudo tee /etc/ascend_install.info
```

**正式安装步骤：**

```bash
# 1. 安装驱动（需要管理员权限）
sudo chmod +x Ascend-hdk-910b-npu-driver_*.run
sudo ./Ascend-hdk-910b-npu-driver_*.run --full

# 2. 如果遇到内核编译问题，手动指定内核路径
# 当提示输入内核路径时，输入：/lib/modules/$(uname -r)/build

# 3. 安装固件
sudo chmod +x Ascend-hdk-910b-npu-firmware_*.run
sudo ./Ascend-hdk-910b-npu-firmware_*.run --full

# 4. 重启系统（重要！）
sudo reboot
```

### 3. 驱动验证
重启后运行检查脚本：
```bash
# 检查驱动安装状态
./check_ascend_driver.sh

# 检查设备文件
ls -la /dev/davinci*
ls -la /dev/davinci_manager

# 检查npu-smi工具
/usr/local/Ascend/driver/tools/npu-smi info
```

## 第二步：CANN工具包安装

### 1. 官方下载地址
- **华为昇腾官网**: https://www.hiascend.com/software/cann
- **开发者社区**: https://www.hiascend.com/developer

### 2. 所需文件
对于ARM64架构的昇腾910B环境，需要下载：
```
Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run
```

### 3. 下载步骤
1. 访问华为昇腾官网
2. 注册并登录开发者账号
3. 进入CANN软件下载页面
4. 选择版本：**8.0.RC1**
5. 选择架构：**linux-aarch64**
6. 选择组件：**toolkit（工具包）**
7. 下载文件到项目根目录

## 版本兼容性

### 当前使用的版本组合
```
- paddlepaddle: 3.1.0
- paddle-custom-npu: 3.1.0
- paddleocr: 3.0.0
- CANN: 8.0.RC1
- 系统架构: ARM64 (aarch64)
```

### 为什么选择CANN 8.0.RC1
1. **兼容性**: 与paddle-custom-npu 3.1.0兼容性良好
2. **稳定性**: RC1版本相对稳定，适合生产环境
3. **功能完整**: 支持昇腾910B的完整功能

## 安装方式

### 方式一：容器内安装（推荐）
项目的Dockerfile.ascend会自动安装CANN工具包：

```dockerfile
COPY Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run /tmp/
RUN chmod +x /tmp/Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run && \
    /tmp/Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run --install --quiet
```

**使用步骤：**
1. 下载CANN工具包到项目根目录
2. 运行构建命令：
   ```bash
   docker build -f Dockerfile.ascend -t paddleocr-ascend:latest .
   ```

### 方式二：宿主机安装
如果宿主机已安装CANN，可以通过挂载方式使用：

```bash
# 宿主机安装CANN
chmod +x Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run
./Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run --install

# Docker运行时挂载
docker run -v /usr/local/Ascend:/usr/local/Ascend:ro ...
```

## 环境变量配置

CANN安装后需要设置以下环境变量（Dockerfile中已自动配置）：

```bash
export LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64:$LD_LIBRARY_PATH
export PATH=/usr/local/Ascend/ascend-toolkit/latest/bin:$PATH
export ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp
export ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest
export ASCEND_TOOLKIT_HOME=/usr/local/Ascend/ascend-toolkit/latest
```

## 验证安装

### 1. 检查CANN安装
```bash
# 检查CANN版本
/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info

# 检查库文件
ls /usr/local/Ascend/ascend-toolkit/latest/lib64/
```

### 2. 检查NPU设备
```bash
# 运行项目提供的检测脚本
./check_ascend_devices.sh

# 或手动检查设备文件
ls -la /dev/davinci*
```

### 3. 验证PaddlePaddle NPU支持
```bash
# 运行诊断脚本
python diagnose_paddle.py

# 或手动测试
python -c "
import paddle
print('Paddle version:', paddle.__version__)
devices = paddle.device.get_all_custom_device_type()
print('Available custom devices:', devices)
if 'npu' in devices:
    print('✅ NPU support detected')
else:
    print('❌ NPU support not found')
"
```

## 常见问题

### 1. 内核编译问题
**问题**: `Rebuild ko has something wrong` 或 `Drv_dkms_env_check failed`
**原因**: 缺少内核开发包或内核版本不匹配
**解决**:
```bash
# 安装内核开发包
sudo apt update
sudo apt install -y linux-headers-$(uname -r) linux-headers-generic
sudo apt install -y build-essential dkms gcc make

# 检查内核开发包是否正确安装
ls /lib/modules/$(uname -r)/build

# 重新安装驱动，手动指定内核路径
sudo ./Ascend-hdk-910b-npu-driver_24.1.rc1_linux-aarch64.run --full
# 当提示输入内核路径时，输入：/lib/modules/$(uname -r)/build
```

### 2. 驱动安装权限错误
**问题**: `ERR_NO:0x0091;ERR_DES:username not permission for root`
**原因**: 昇腾安装程序检查权限配置文件失败
**解决**:
```bash
# 删除旧的权限配置文件
sudo rm -f /etc/ascend_install.info

# 创建新的权限配置
echo 'install_username=root' | sudo tee /etc/ascend_install.info

# 重新安装驱动
sudo ./Ascend-hdk-910b-npu-driver_24.1.rc1_linux-aarch64.run --full
```

### 2. 下载问题
**问题**: 无法访问华为昇腾官网
**解决**:
- 检查网络连接
- 使用VPN或代理
- 联系网络管理员

### 3. CANN权限问题
**问题**: CANN安装时提示权限不足
**解决**:
```bash
chmod +x Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run
sudo ./Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run --install
```

### 3. 版本不匹配
**问题**: CANN版本与paddle-custom-npu不兼容
**解决**:
- 确保使用CANN 8.0.RC1
- 检查paddle-custom-npu版本为3.1.0
- 参考VERSION_COMPATIBILITY.md文档

### 4. 设备检测失败
**问题**: 无法检测到NPU设备
**解决**:
- 检查昇腾驱动是否正确安装
- 确认设备文件存在：`ls /dev/davinci*`
- 检查设备权限和用户组

## 部署建议

### 1. 生产环境
- 优先使用宿主机安装CANN，容器挂载使用
- 确保CANN版本与硬件驱动匹配
- 定期备份CANN配置

### 2. 开发环境
- 可以使用容器内安装方式
- 便于版本管理和环境隔离
- 适合快速测试和开发

### 3. 版本升级
- 升级前备份当前环境
- 参考官方兼容性文档
- 在测试环境先验证

## 技术支持

如果遇到CANN相关问题，可以：

1. **查阅官方文档**: https://www.hiascend.com/document
2. **访问开发者社区**: https://www.hiascend.com/developer
3. **提交技术支持工单**: 通过华为昇腾官网
4. **参考项目文档**: 
   - `VERSION_COMPATIBILITY.md` - 版本兼容性
   - `troubleshooting.md` - 故障排除
   - `check_ascend_devices.sh` - 设备检测

## 更新日志

- **2025-01-28**: 创建CANN 8.0.RC1安装说明，配合paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0使用
