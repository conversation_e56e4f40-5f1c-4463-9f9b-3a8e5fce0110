#!/bin/bash

# 更新现有容器中的OCR解析逻辑

set -e

CONTAINER_NAME="paddleocr-offline"

echo "========================================="
echo "更新容器中的OCR解析逻辑"
echo "========================================="
echo

# 检查容器是否存在
if ! docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "❌ 错误: 容器 $CONTAINER_NAME 不存在"
    exit 1
fi

# 检查容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "🔄 启动容器..."
    docker start "$CONTAINER_NAME"
    sleep 5
fi

echo "📁 复制更新后的文件到容器..."

# 复制更新后的文件到容器
docker cp ocr_subprocess.py "$CONTAINER_NAME":/work/
docker cp app_simple.py "$CONTAINER_NAME":/work/

echo "🔄 重启容器以应用更改..."
docker restart "$CONTAINER_NAME"

echo "⏳ 等待服务启动..."
sleep 10

echo "🧪 测试服务..."
if curl -s http://localhost:9527/health >/dev/null 2>&1; then
    echo "✅ 服务健康检查通过"
    echo
    echo "🎉 解析逻辑更新完成!"
    echo
    echo "现在OCR服务应该能正确过滤错误信息，只返回实际的OCR结果"
    echo
    echo "测试命令:"
    echo "  curl -X POST -F 'file=@test_image.jpg' http://localhost:9527/ocr"
else
    echo "⚠️  服务可能还在启动中，请稍后再试"
    echo "查看日志: docker logs $CONTAINER_NAME"
fi
