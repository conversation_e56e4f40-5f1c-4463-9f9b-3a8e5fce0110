#!/bin/bash

# 构建离线PaddleOCR镜像脚本
# 包含预下载的模型，适用于无网环境部署

set -e

echo "========================================="
echo "构建离线PaddleOCR镜像"
echo "========================================="
echo

# 检查必要文件
REQUIRED_FILES=(
    "Dockerfile.offline"
    "paddlex_models.tar.gz"
    "app_simple.py"
    "ocr_subprocess.py"
    "run_simple.py"
    "requirements_simple.txt"
)

echo "🔍 检查必要文件..."
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ 找到文件: $file"
    fi
done

# 检查模型文件大小
MODEL_SIZE=$(du -h paddlex_models.tar.gz | cut -f1)
echo "📦 模型文件大小: $MODEL_SIZE"

# 设置镜像信息
IMAGE_NAME="paddleocr-offline-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo
echo "🚀 开始构建镜像: $FULL_IMAGE_NAME"
echo "构建上下文: $(pwd)"
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

# 构建Docker镜像（兼容旧版本Docker）
docker build \
    --platform linux/arm64 \
    -f Dockerfile.offline \
    -t "$FULL_IMAGE_NAME" \
    --no-cache \
    .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ 离线镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    # 显示镜像大小
    IMAGE_SIZE=$(docker images --format "table {{.Size}}" "$FULL_IMAGE_NAME" | tail -n 1)
    echo "📦 最终镜像大小: $IMAGE_SIZE"
    echo
    
    echo "🎉 离线镜像构建完成!"
    echo
    echo "📋 后续步骤:"
    echo
    echo "1. 导出镜像用于离线部署:"
    echo "   docker save -o paddleocr-offline-ascend.tar $FULL_IMAGE_NAME"
    echo
    echo "2. 在目标服务器上导入镜像:"
    echo "   docker load -i paddleocr-offline-ascend.tar"
    echo
    echo "3. 启动离线服务:"
    echo "   docker run -d --name paddleocr-offline \\"
    echo "     --privileged --network=host --shm-size=128G \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo
    echo "4. 测试服务:"
    echo "   curl http://localhost:9527/health"
    echo
    echo "📝 特性:"
    echo "   ✅ 包含所有预下载的OCR模型"
    echo "   ✅ 支持NPU加速"
    echo "   ✅ 无需网络连接"
    echo "   ✅ 开箱即用"
    echo "   ✅ 包含完整的PaddleOCR环境"
    echo
    echo "🔍 验证镜像内容:"
    echo "   docker run --rm $FULL_IMAGE_NAME which paddleocr"
    echo "   docker run --rm $FULL_IMAGE_NAME ls -la /root/.paddlex/official_models/"
    
else
    echo
    echo "❌ 镜像构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo "请检查构建日志中的错误信息"
    exit 1
fi
