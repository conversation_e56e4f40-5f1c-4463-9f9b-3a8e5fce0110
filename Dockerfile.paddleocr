FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    git \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置pip源为清华大学镜像
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

# 升级pip和安装必要的工具
RUN pip install --upgrade pip setuptools wheel

# 安装基本依赖
RUN pip install --no-cache-dir flask==3.0.0 werkzeug==3.0.1 "numpy==1.26.4" "Pillow>=10.1.0" python-dotenv==1.0.0 "PyMuPDF>=1.23.0"

# 安装paddlepaddle和paddleocr
# 使用--no-deps选项避免依赖冲突
RUN pip install --no-cache-dir paddlepaddle==3.1.0
RUN pip install --no-cache-dir paddleocr==3.1.0 --no-deps
RUN pip install --no-cache-dir shapely pyclipper imgaug lmdb tqdm visualdl rapidfuzz

# 复制项目文件
COPY . .

# 暴露端口
EXPOSE 9527

# 启动应用
CMD ["python", "run.py"]
