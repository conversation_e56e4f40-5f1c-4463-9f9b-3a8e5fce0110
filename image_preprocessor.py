#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片预处理工具
针对ARM64架构优化，避免段错误
"""

import os
from PIL import Image
import tempfile

class ImagePreprocessor:
    def __init__(self, max_size=640):
        """
        初始化图片预处理器
        
        Args:
            max_size: 图片最大边长限制（默认640px，基于社区反馈）
        """
        self.max_size = max_size
        print(f"🖼️ 图片预处理器初始化，最大尺寸: {max_size}px")
    
    def preprocess_image(self, image_path):
        """
        预处理图片，确保符合ARM64优化要求
        
        Args:
            image_path: 输入图片路径
            
        Returns:
            处理后的图片路径
        """
        try:
            print(f"🔍 检查图片: {image_path}")
            
            # 打开图片
            with Image.open(image_path) as img:
                original_size = img.size
                print(f"原始尺寸: {original_size[0]}x{original_size[1]}")
                
                # 检查是否需要调整尺寸
                max_dimension = max(original_size)
                
                if max_dimension <= self.max_size:
                    print(f"✅ 图片尺寸符合要求，无需调整")
                    return image_path
                
                # 计算新尺寸
                scale_ratio = self.max_size / max_dimension
                new_width = int(original_size[0] * scale_ratio)
                new_height = int(original_size[1] * scale_ratio)
                
                print(f"🔧 调整尺寸: {original_size[0]}x{original_size[1]} -> {new_width}x{new_height}")
                print(f"缩放比例: {scale_ratio:.2f}")
                
                # 调整图片尺寸
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 保存到临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.png', prefix='arm64_optimized_')
                os.close(temp_fd)  # 关闭文件描述符
                
                # 确保保存为RGB模式
                if resized_img.mode != 'RGB':
                    resized_img = resized_img.convert('RGB')
                
                resized_img.save(temp_path, 'PNG', optimize=True)
                
                print(f"✅ 图片已优化保存: {temp_path}")
                return temp_path
                
        except Exception as e:
            print(f"❌ 图片预处理失败: {e}")
            # 如果预处理失败，返回原图片
            return image_path
    
    def cleanup_temp_file(self, file_path):
        """清理临时文件"""
        try:
            if file_path and os.path.exists(file_path) and 'arm64_optimized_' in file_path:
                os.remove(file_path)
                print(f"🧹 已清理临时文件: {file_path}")
        except Exception as e:
            print(f"⚠️ 清理临时文件失败: {e}")
    
    def get_image_info(self, image_path):
        """获取图片信息"""
        try:
            with Image.open(image_path) as img:
                return {
                    'size': img.size,
                    'mode': img.mode,
                    'format': img.format,
                    'max_dimension': max(img.size),
                    'needs_resize': max(img.size) > self.max_size
                }
        except Exception as e:
            print(f"❌ 获取图片信息失败: {e}")
            return None

def test_preprocessor():
    """测试图片预处理器"""
    print("🧪 测试图片预处理器")
    print("=" * 30)
    
    # 创建测试图片
    test_images = []
    
    # 创建一个大尺寸图片（会被调整）
    large_img = Image.new('RGB', (1200, 800), color='white')
    large_path = 'test_large.png'
    large_img.save(large_path)
    test_images.append(large_path)
    print(f"✅ 创建大尺寸测试图片: {large_path} (1200x800)")
    
    # 创建一个小尺寸图片（不会被调整）
    small_img = Image.new('RGB', (400, 300), color='lightblue')
    small_path = 'test_small.png'
    small_img.save(small_path)
    test_images.append(small_path)
    print(f"✅ 创建小尺寸测试图片: {small_path} (400x300)")
    
    # 测试预处理器
    preprocessor = ImagePreprocessor(max_size=640)
    
    for img_path in test_images:
        print(f"\n📋 测试图片: {img_path}")
        
        # 获取图片信息
        info = preprocessor.get_image_info(img_path)
        if info:
            print(f"   尺寸: {info['size']}")
            print(f"   最大边长: {info['max_dimension']}")
            print(f"   需要调整: {info['needs_resize']}")
        
        # 预处理图片
        processed_path = preprocessor.preprocess_image(img_path)
        
        if processed_path != img_path:
            # 验证处理后的图片
            processed_info = preprocessor.get_image_info(processed_path)
            if processed_info:
                print(f"   处理后尺寸: {processed_info['size']}")
                print(f"   处理后最大边长: {processed_info['max_dimension']}")
            
            # 清理临时文件
            preprocessor.cleanup_temp_file(processed_path)
    
    # 清理测试文件
    for img_path in test_images:
        try:
            os.remove(img_path)
            print(f"🧹 已清理测试文件: {img_path}")
        except:
            pass
    
    print("\n✅ 图片预处理器测试完成")

if __name__ == "__main__":
    test_preprocessor()
