#!/bin/bash

# ARM64服务器本地构建脚本
# 用于在ARM64 Linux服务器上直接构建PaddleOCR Docker镜像

echo "🚀 ARM64服务器本地构建PaddleOCR Docker镜像"
echo "============================================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    echo "安装命令（Ubuntu/Debian）:"
    echo "  curl -fsSL https://get.docker.com -o get-docker.sh"
    echo "  sudo sh get-docker.sh"
    echo "  sudo usermod -aG docker \$USER"
    exit 1
fi

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    echo "启动命令:"
    echo "  sudo systemctl start docker"
    echo "  sudo systemctl enable docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查必要文件
echo "🔍 检查项目文件..."

if [ ! -f "paddlex_models.tar.gz" ]; then
    echo "❌ 模型文件 paddlex_models.tar.gz 不存在"
    echo "请确保已将PaddleOCR模型文件放置在项目根目录"
    echo "模型文件应包含以下内容:"
    echo "  - PP-LCNet_x1_0_doc_ori"
    echo "  - PP-LCNet_x1_0_textline_ori"
    echo "  - PP-OCRv5_server_det"
    echo "  - PP-OCRv5_server_rec"
    echo "  - UVDoc"
    exit 1
fi

if [ ! -f "Dockerfile.arm64" ]; then
    echo "❌ Dockerfile.arm64 不存在"
    echo "请确保项目文件完整"
    exit 1
fi

required_files=("app.py" "ocr_processor.py" "run.py" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 检测到模型文件: paddlex_models.tar.gz"
echo "✅ 项目文件检查通过"

# 显示模型文件信息
echo "📦 模型文件信息:"
ls -lh paddlex_models.tar.gz

# 设置镜像名称和标签
IMAGE_NAME="paddleocr-service"
IMAGE_TAG="arm64-latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "🔨 开始构建ARM64镜像..."
echo "镜像名称: ${FULL_IMAGE_NAME}"
echo "使用Dockerfile: Dockerfile.arm64"
echo "构建上下文: $(pwd)"

# 显示系统信息
echo "📋 系统信息:"
echo "  架构: $(uname -m)"
echo "  内核: $(uname -r)"
echo "  发行版: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"

# 检查可用磁盘空间
available_space=$(df . | tail -1 | awk '{print $4}')
if [ "$available_space" -lt 5000000 ]; then  # 5GB
    echo "⚠️ 警告: 可用磁盘空间不足5GB，构建可能失败"
    echo "当前可用空间: $(df -h . | tail -1 | awk '{print $4}')"
fi

# 清理旧镜像（可选）
if docker images | grep -q "${IMAGE_NAME}.*${IMAGE_TAG}"; then
    echo "🧹 发现旧镜像，是否清理？(y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        docker rmi "${FULL_IMAGE_NAME}" >/dev/null 2>&1
        echo "✅ 旧镜像已清理"
    fi
fi

# 开始构建
echo "⏳ 开始构建镜像，这可能需要10-30分钟..."
start_time=$(date +%s)

if docker build -f Dockerfile.arm64 -t "${FULL_IMAGE_NAME}" .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ ARM64镜像构建成功: ${FULL_IMAGE_NAME}"
    echo "⏱️ 构建耗时: ${duration}秒"
else
    echo "❌ ARM64镜像构建失败"
    echo "请检查构建日志中的错误信息"
    exit 1
fi

# 显示镜像信息
echo "📋 镜像信息:"
docker images | grep "${IMAGE_NAME}"

# 显示镜像大小
image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "${IMAGE_NAME}" | grep "${IMAGE_TAG}" | awk '{print $3}')
echo "📦 镜像大小: ${image_size}"

echo ""
echo "🎉 构建完成！"
echo "==============="

# 显示后续操作选项
echo "📋 后续操作选项:"
echo ""
echo "1️⃣ 直接运行容器:"
echo "  docker run -d --name paddleocr-service -p 9527:9527 ${FULL_IMAGE_NAME}"
echo ""
echo "2️⃣ 导出镜像文件:"
echo "  docker save ${FULL_IMAGE_NAME} -o paddleocr-service-arm64.tar"
echo ""
echo "3️⃣ 运行部署脚本:"
echo "  ./deploy_arm64.sh"
echo ""
echo "4️⃣ 测试镜像:"
echo "  python3 test_deployment.py"
echo ""

# 询问是否立即部署
echo "🚀 是否立即部署服务？(y/N)"
read -r deploy_response
if [[ "$deploy_response" =~ ^[Yy]$ ]]; then
    echo "开始部署..."
    if [ -f "deploy_arm64.sh" ]; then
        chmod +x deploy_arm64.sh
        ./deploy_arm64.sh --build-only
    else
        echo "部署脚本不存在，手动启动容器..."
        docker run -d \
            --name paddleocr-service \
            -p 9527:9527 \
            --restart unless-stopped \
            --memory 4g \
            --cpus 2 \
            "${FULL_IMAGE_NAME}"
        
        if [ $? -eq 0 ]; then
            echo "✅ 容器启动成功"
            echo "🏥 健康检查: curl http://localhost:9527/health"
        else
            echo "❌ 容器启动失败"
        fi
    fi
fi

echo "✅ 构建流程完成！"
