# 离线PaddleOCR服务Docker镜像
# 包含预下载的模型，适用于无网环境部署

FROM ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84

WORKDIR /work

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装基本工具（如果需要）
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装PaddleOCR及其依赖（使用稳定版本）
RUN echo "安装PaddlePaddle 3.1.0..." && \
    pip install paddlepaddle==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/ && \
    echo "安装paddle-custom-npu 3.1.0..." && \
    pip install paddle-custom-npu==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/npu/ && \
    echo "PaddlePaddle 3.1.0安装完成"

# 安装PaddleOCR
RUN echo "安装PaddleOCR 3.1.0..." && \
    pip install paddleocr==3.1.0 && \
    echo "PaddleOCR安装完成"

# 更新numpy版本
RUN echo "更新numpy版本..." && \
    pip uninstall -y numpy && \
    pip install numpy==1.26.4 && \
    echo "numpy 1.26.4安装完成"

# 验证安装的版本（跳过需要昇腾环境的验证）
RUN echo "验证安装的版本..." && \
    echo "检查已安装的包..." && \
    pip list | grep -E "(paddle|numpy)" && \
    echo "验证基础模块..." && \
    python -c "import os; os.environ['CUSTOM_DEVICE_ROOT'] = ''; os.environ['FLAGS_init_allocated_mem'] = 'false'; os.environ['PADDLE_DISABLE_CUSTOM_DEVICE'] = '1'; import numpy; print('NumPy version:', numpy.__version__); print('基础验证完成')" && \
    echo "版本验证完成"

# 验证paddleocr命令是否可用（简单验证）
RUN echo "验证paddleocr命令..." && \
    which paddleocr && \
    echo "paddleocr命令路径验证成功" && \
    echo "注意：完整功能验证将在运行时进行"

# 复制预下载的模型文件
COPY paddlex_models.tar.gz /tmp/
RUN cd /root && \
    tar -xzf /tmp/paddlex_models.tar.gz && \
    rm /tmp/paddlex_models.tar.gz && \
    echo "模型文件已安装到 /root/.paddlex/"

# 验证模型文件
RUN ls -la /root/.paddlex/official_models/ && \
    du -sh /root/.paddlex/

# 复制应用代码
COPY app_simple.py /work/
COPY ocr_subprocess.py /work/
COPY run_simple.py /work/
COPY requirements_simple.txt /work/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_simple.txt

# 设置权限
RUN chmod +x /work/*.py

# 创建临时目录并设置权限
RUN mkdir -p /tmp && chmod 777 /tmp

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9527/health || exit 1

# 暴露端口
EXPOSE 9527

# 启动命令
CMD ["python", "run_simple.py"]
