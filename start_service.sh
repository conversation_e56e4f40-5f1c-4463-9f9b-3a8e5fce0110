#!/bin/bash

# PaddleOCR服务启动脚本

echo "🚀 启动PaddleOCR服务..."

# 停止现有容器
echo "🧹 清理现有容器..."
docker stop paddleocr-offline 2>/dev/null || true
docker rm paddleocr-offline 2>/dev/null || true

# 启动基础容器
echo "📦 启动基础容器..."
docker run -d \
  --name paddleocr-offline \
  --privileged \
  --network=host \
  --shm-size=128G \
  -w=/work \
  -v $(pwd):/work \
  -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
  -v /usr/local/dcmi:/usr/local/dcmi \
  -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
  paddleocr-offline-ascend:latest \
  tail -f /dev/null

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 在容器内启动Flask应用
echo "🔥 启动Flask应用..."
echo "请手动执行以下命令来启动服务："
echo
echo "docker exec -it paddleocr-offline bash"
echo "然后在容器内运行："
echo "cd /work"
echo "export FLASK_ENV=development"
echo "export FLASK_DEBUG=1"
echo "python -c \"from app_simple import app; app.run(host='0.0.0.0', port=9527, debug=True)\""
echo
echo "或者运行以下一键命令："
echo "docker exec -it paddleocr-offline bash -c \"cd /work && export FLASK_ENV=development && export FLASK_DEBUG=1 && python -c 'from app_simple import app; app.run(host=\\\"0.0.0.0\\\", port=9527, debug=True)'\""

echo
echo "✅ 基础容器启动成功!"
echo "📦 容器名称: paddleocr-offline"
echo
echo "📋 管理命令:"
echo "  进入容器: docker exec -it paddleocr-offline bash"
echo "  查看日志: docker logs paddleocr-offline"
echo "  停止容器: docker stop paddleocr-offline"
echo
echo "🎯 启动服务后的测试命令:"
echo "  健康检查: curl http://localhost:9527/health"
echo "  OCR测试: curl -X POST -F 'file=@kai.jpg' http://localhost:9527/ocr"
