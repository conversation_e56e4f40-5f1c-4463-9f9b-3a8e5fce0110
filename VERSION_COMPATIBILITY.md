# 版本兼容性说明

本文档说明了PaddleOCR项目在不同硬件环境下的版本兼容性和推荐配置。

## 硬件环境分类

### 1. NVIDIA GPU环境
适用于配备NVIDIA GPU的x86_64架构服务器。

**推荐版本组合：**
```
- paddlepaddle-gpu: 3.0.0
- paddleocr: 3.0.0
- CUDA: 12.6
- cuDNN: 8.x
```

**安装命令：**
```bash
pip install paddlepaddle-gpu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cu126/
pip install paddleocr==3.0.0
```

**使用配置文件：**
- `requirements.txt`
- `Dockerfile`
- `docker-compose.yml`

### 2. 昇腾NPU环境（推荐）
适用于配备昇腾910B等NPU的ARM64架构服务器。

**用户指定的版本组合：**
```
- paddlepaddle: 3.1.0
- paddle-custom-npu: 3.1.0
- paddleocr: 3.0.0
- CANN: 8.0.RC1
- Python: 3.8-3.11
- 系统架构: ARM64 (aarch64)
```

**安装命令：**
```bash
pip install paddlepaddle==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/
pip install paddle-custom-npu==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/npu/
pip install paddleocr==3.0.0
```

**使用配置文件：**
- `requirements.ascend.txt`
- `Dockerfile.ascend`
- `docker-compose.ascend.yml`

## 版本兼容性矩阵

### 昇腾NPU版本兼容性

| paddlepaddle | paddle-custom-npu | paddleocr | CANN | 兼容性 | 备注 |
|--------------|-------------------|-----------|------|--------|------|
| 3.1.0 | 3.1.0 | 3.0.0 | 8.0.RC1 | ✅ 当前使用 | 用户指定的版本组合 |
| 2.6.2 | 3.0.0 | 2.7.3 | 8.0.RC3 | ✅ 备选 | 之前测试的稳定组合 |
| 2.6.2 | 3.0.0 | 2.7.3 | 8.1.RC1 | ✅ 备选 | 之前测试的稳定组合 |
| 3.0.0 | 3.0.0 | 3.0.0 | 8.1.RC1 | ⚠️ 测试中 | 可能存在兼容性问题 |

### NVIDIA GPU版本兼容性

| paddlepaddle-gpu | paddleocr | CUDA | 兼容性 | 备注 |
|------------------|-----------|------|--------|------|
| 3.0.0 | 3.0.0 | 12.6 | ✅ 推荐 | 官方推荐组合 |
| 3.0.0 | 2.7.3 | 12.6 | ✅ 兼容 | 向下兼容 |

## 重要说明

### 关于PaddleOCR NPU版本
- **PaddleOCR本身没有专门的NPU版本**
- NPU支持是通过底层的`paddle-custom-npu`包实现的
- PaddleOCR会自动检测并使用可用的硬件加速

### 关于版本选择原则
1. **稳定性优先**：优先选择经过验证的稳定版本组合
2. **硬件匹配**：确保软件版本与硬件驱动版本匹配
3. **向下兼容**：新版本PaddlePaddle通常向下兼容旧版本PaddleOCR

### 昇腾NPU特殊注意事项
1. **CANN版本**：确保CANN工具包版本与paddle-custom-npu兼容
2. **系统架构**：昇腾NPU通常部署在ARM64架构系统上
3. **内存管理**：NPU环境需要特殊的内存管理配置
4. **段错误问题**：使用推荐的稳定版本组合可以避免常见的段错误

## 部署建议

### 昇腾910B部署步骤
1. **环境检测**：运行`./check_ascend_devices.sh`检测NPU设备
2. **版本验证**：使用`diagnose_paddle.py`验证环境
3. **稳定版本**：优先使用推荐的稳定版本组合
4. **逐步升级**：如需使用新版本，先在测试环境验证

### 构建命令
```bash
# 昇腾NPU环境
docker build -f Dockerfile.ascend -t paddleocr-ascend:stable .
docker-compose -f docker-compose.ascend.yml up -d

# NVIDIA GPU环境
docker build -f Dockerfile -t paddleocr-gpu:latest .
docker-compose up -d
```

## 故障排除

### 常见问题
1. **段错误**：通常由版本不兼容引起，使用推荐的稳定版本组合
2. **NPU检测失败**：检查CANN工具包安装和环境变量设置
3. **模型加载失败**：确保网络连接正常，模型会自动下载

### 版本回退
如果遇到兼容性问题，可以回退到之前测试的稳定版本：
```bash
pip uninstall paddlepaddle paddle-custom-npu paddleocr
pip install paddlepaddle==2.6.2 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/
pip install paddle-custom-npu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/npu/
pip install paddleocr==2.7.3
```

## 更新日志

- **2025-01-28**：更新为用户指定版本：paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0 + paddleocr 3.0.0 + CANN 8.0.RC1
- **项目初始**：支持NVIDIA GPU和昇腾NPU双环境部署
