#!/bin/bash

# 昇腾NPU版本Docker构建脚本 (Linux)
# 适用于ARM64架构的昇腾NPU服务器

set -e

echo "========================================="
echo "PaddleOCR 昇腾NPU版本 Docker构建脚本"
echo "========================================="
echo

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    exit 1
fi

# 检查架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "⚠️  警告: 当前系统不是ARM64架构，构建的镜像可能无法在昇腾NPU服务器上运行"
    read -p "是否继续构建? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "构建已取消"
        exit 1
    fi
fi

# 检查昇腾CANN工具包
CANN_FILE="Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run"
if [ ! -f "$CANN_FILE" ]; then
    echo "⚠️  警告: 未找到昇腾CANN工具包文件: $CANN_FILE"
    echo "   Docker镜像将不包含CANN工具包，需要在宿主机上安装"
    echo "   或者将CANN工具包文件放置在项目根目录"
    echo
fi

# 检查必要文件
REQUIRED_FILES=("Dockerfile.ascend" "requirements.ascend.txt" "app.py" "ocr_processor.py" "run.py")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必要文件: $file"
        exit 1
    fi
done

echo "✓ 所有必要文件检查完成"
echo

# 设置镜像标签
IMAGE_NAME="paddleocr-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "开始构建Docker镜像: $FULL_IMAGE_NAME"
echo "构建上下文: $(pwd)"
echo

# 构建Docker镜像
echo "执行构建命令..."
echo "推荐的构建命令："
echo "docker build --platform linux/arm64 -f Dockerfile.ascend -t paddleocr-arm64-server:latest --no-cache --progress=plain ."
echo
docker build \
    --platform linux/arm64 \
    -f Dockerfile.ascend \
    -t "$FULL_IMAGE_NAME" \
    --no-cache \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo
    echo "✅ Docker镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo
    
    # 显示镜像信息
    echo "镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    echo "后续步骤:"
    echo "1. 运行容器 (自动检测昇腾设备):"
    echo "   ./run_container.sh"
    echo
    echo "2. 使用Docker Compose运行:"
    echo "   docker-compose -f docker-compose.ascend.yml up -d"
    echo
    echo "3. 手动运行 (昇腾NPU模式):"
    echo "   docker run --name paddleocr-ascend -p 9527:9527 \\"
    echo "     --device=/dev/davinci0:/dev/davinci0 \\"
    echo "     --device=/dev/davinci_manager:/dev/davinci_manager \\"
    echo "     --device=/dev/devmm_svm:/dev/devmm_svm \\"
    echo "     --device=/dev/hisi_hdc:/dev/hisi_hdc \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend:ro \\"
    echo "     -d $FULL_IMAGE_NAME"
    echo
    echo "4. 测试服务:"
    echo "   curl http://localhost:9527/health"
    
else
    echo
    echo "❌ Docker镜像构建失败!"
    echo "请检查构建日志中的错误信息"
    exit 1
fi
