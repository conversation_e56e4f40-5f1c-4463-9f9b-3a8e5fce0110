#!/bin/bash

# 最终构建脚本 - 使用简化的Dockerfile

set -e

echo "========================================="
echo "最终构建离线PaddleOCR镜像"
echo "使用简化的构建流程"
echo "========================================="
echo

# 设置镜像信息
IMAGE_NAME="paddleocr-offline-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="paddleocr-offline"

echo "🧹 清理现有资源..."

# 停止并删除现有容器
if docker ps -a | grep -q "$CONTAINER_NAME" 2>/dev/null; then
    echo "停止容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    echo "删除容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 删除现有镜像
if docker images | grep -q "$IMAGE_NAME" 2>/dev/null; then
    echo "删除镜像: $FULL_IMAGE_NAME"
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
fi

echo "✅ 清理完成"
echo

# 检查必要文件
echo "🔍 检查必要文件..."
REQUIRED_FILES=("Dockerfile.simple" "paddlex_models.tar.gz" "app_simple.py" "ocr_subprocess.py" "run_simple.py" "requirements_simple.txt")

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ 找到文件: $file"
    fi
done

# 检查模型文件大小
MODEL_SIZE=$(du -h paddlex_models.tar.gz | cut -f1)
echo "📦 模型文件大小: $MODEL_SIZE"

echo
echo "🚀 开始构建镜像: $FULL_IMAGE_NAME"
echo "使用简化Dockerfile: Dockerfile.simple"
echo
echo "📝 版本信息:"
echo "  - PaddlePaddle: 3.1.0 (稳定版)"
echo "  - paddle-custom-npu: 3.1.0 (稳定版)"
echo "  - PaddleOCR: 3.1.0"
echo "  - NumPy: 1.26.4"
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

# 使用简化的Dockerfile构建
echo "执行构建命令..."
docker build -f Dockerfile.simple -t "$FULL_IMAGE_NAME" --no-cache .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ 镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images | grep "$IMAGE_NAME"
    echo
    
    # 简单验证镜像内容
    echo "🔍 验证镜像内容..."
    docker run --rm "$FULL_IMAGE_NAME" bash -c "
echo '📦 已安装的关键包:'
pip list | grep -E '(paddle|numpy|flask)' | head -10

echo
echo '📁 模型文件:'
ls -la /root/.paddlex/official_models/ | head -5

echo
echo '🔧 应用文件:'
ls -la /work/*.py

echo
echo '✅ 镜像内容验证完成'
"
    
    echo
    echo "🎉 构建完成!"
    echo
    echo "📋 立即部署:"
    echo "   ./quick_deploy_fixed.sh"
    echo
    echo "📋 手动部署:"
    echo "   docker run -d --name paddleocr-offline \\"
    echo "     --privileged --network=host --shm-size=128G \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo
    echo "📋 测试服务:"
    echo "   curl http://localhost:9527/health"
    echo "   curl -X POST -F 'file=@test_image.jpg' http://localhost:9527/ocr"
    echo
    echo "📋 导出镜像:"
    echo "   docker save -o paddleocr-offline-final.tar $FULL_IMAGE_NAME"
    echo
    echo "✨ 特性:"
    echo "   ✅ 使用稳定版本的PaddlePaddle和PaddleOCR"
    echo "   ✅ 包含预下载的OCR模型"
    echo "   ✅ 支持NPU加速"
    echo "   ✅ 完全离线运行"
    echo "   ✅ 改进的错误处理"
    
else
    echo
    echo "❌ 镜像构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    echo "🔧 故障排除:"
    echo "1. 检查网络连接"
    echo "2. 确保磁盘空间充足"
    echo "3. 检查模型文件完整性"
    echo "4. 查看详细错误信息"
    exit 1
fi
