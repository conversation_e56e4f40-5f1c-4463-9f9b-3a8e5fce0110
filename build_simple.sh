#!/bin/bash

# 简化的构建脚本，兼容旧版本Docker

set -e

echo "========================================="
echo "构建离线PaddleOCR镜像（兼容版本）"
echo "========================================="
echo

# 设置镜像信息
IMAGE_NAME="paddleocr-offline-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="paddleocr-offline"

echo "🧹 清理现有资源..."

# 停止并删除现有容器
if docker ps -a | grep -q "$CONTAINER_NAME" 2>/dev/null; then
    echo "停止容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    echo "删除容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 删除现有镜像
if docker images | grep -q "$IMAGE_NAME" 2>/dev/null; then
    echo "删除镜像: $FULL_IMAGE_NAME"
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
fi

echo "✅ 清理完成"
echo

# 检查必要文件
echo "🔍 检查必要文件..."
REQUIRED_FILES=("Dockerfile.offline" "paddlex_models.tar.gz" "app_simple.py" "ocr_subprocess.py" "run_simple.py" "requirements_simple.txt")

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ 找到文件: $file"
    fi
done

echo
echo "🚀 开始构建镜像: $FULL_IMAGE_NAME"
echo "使用稳定版本组合..."
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

# 简化的构建命令（兼容旧版本Docker）
echo "执行构建命令..."
docker build -f Dockerfile.offline -t "$FULL_IMAGE_NAME" --no-cache .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ 镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images | grep "$IMAGE_NAME"
    echo
    
    # 验证镜像中的版本
    echo "🔍 验证镜像中的版本..."
    echo "正在检查PaddlePaddle版本..."
    docker run --rm "$FULL_IMAGE_NAME" python -c "
try:
    import paddle
    print('✅ PaddlePaddle version:', paddle.__version__)
except Exception as e:
    print('❌ PaddlePaddle error:', e)

try:
    import paddleocr
    print('✅ PaddleOCR version:', paddleocr.__version__)
except Exception as e:
    print('❌ PaddleOCR error:', e)

try:
    import paddle_custom_device
    print('✅ paddle-custom-npu: installed')
except Exception as e:
    print('⚠️  paddle-custom-npu error:', e)

try:
    import numpy
    print('✅ NumPy version:', numpy.__version__)
except Exception as e:
    print('❌ NumPy error:', e)
"
    
    echo
    echo "🎉 构建完成!"
    echo
    echo "📋 下一步操作:"
    echo
    echo "1. 快速部署:"
    echo "   ./quick_deploy_fixed.sh"
    echo
    echo "2. 手动启动容器:"
    echo "   docker run -d --name paddleocr-offline \\"
    echo "     --privileged --network=host --shm-size=128G \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo
    echo "3. 测试服务:"
    echo "   curl http://localhost:9527/health"
    echo
    echo "4. 导出镜像:"
    echo "   docker save -o paddleocr-offline-fixed.tar $FULL_IMAGE_NAME"
    
else
    echo
    echo "❌ 镜像构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    echo "🔧 可能的解决方案:"
    echo "1. 检查网络连接"
    echo "2. 确保有足够的磁盘空间"
    echo "3. 检查Docker版本兼容性"
    echo "4. 查看上面的错误信息"
    exit 1
fi
