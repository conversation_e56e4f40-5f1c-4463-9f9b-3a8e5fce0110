#!/bin/bash

# ARM64服务器本地构建和部署脚本
# 用于在ARM64 Linux服务器上直接构建和部署PaddleOCR服务

echo "🚀 PaddleOCR ARM64本地构建和部署脚本"
echo "===================================="

# 配置参数
IMAGE_NAME="paddleocr-service:arm64-latest"
CONTAINER_NAME="paddleocr-service"
PORT="9527"
BUILD_MODE="local"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --load-only)
            BUILD_MODE="load"
            IMAGE_FILE="paddleocr-service-arm64.tar"
            shift
            ;;
        --build-only)
            BUILD_MODE="build"
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "用法: $0 [--load-only|--build-only]"
            echo "  --load-only: 仅加载已有镜像文件"
            echo "  --build-only: 仅构建镜像，不部署"
            echo "  默认: 本地构建并部署"
            exit 1
            ;;
    esac
done

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    echo "   sudo systemctl start docker"
    exit 1
fi

echo "✅ Docker环境检查通过"
echo "📋 构建模式: $BUILD_MODE"

if [ "$BUILD_MODE" = "load" ]; then
    # 加载模式：从tar文件加载镜像
    if [ ! -f "$IMAGE_FILE" ]; then
        echo "❌ 镜像文件 $IMAGE_FILE 不存在"
        echo "请确保已将镜像文件传输到当前目录"
        exit 1
    fi
    echo "✅ 发现镜像文件: $IMAGE_FILE"
elif [ "$BUILD_MODE" = "local" ] || [ "$BUILD_MODE" = "build" ]; then
    # 构建模式：检查源文件
    if [ ! -f "paddlex_models.tar.gz" ]; then
        echo "❌ 模型文件 paddlex_models.tar.gz 不存在"
        echo "请确保已将PaddleOCR模型文件放置在项目根目录"
        exit 1
    fi

    if [ ! -f "Dockerfile.arm64" ]; then
        echo "❌ Dockerfile.arm64 不存在"
        echo "请确保项目文件完整"
        exit 1
    fi

    echo "✅ 发现模型文件: paddlex_models.tar.gz"
    echo "✅ 发现构建文件: Dockerfile.arm64"
fi

# 停止并删除旧容器（如果存在）
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🧹 清理旧容器..."
    docker stop "$CONTAINER_NAME" >/dev/null 2>&1
    docker rm "$CONTAINER_NAME" >/dev/null 2>&1
    echo "✅ 旧容器已清理"
fi

# 根据模式执行不同操作
if [ "$BUILD_MODE" = "load" ]; then
    # 加载模式：从tar文件加载镜像
    echo "📦 加载Docker镜像..."
    if docker load -i "$IMAGE_FILE"; then
        echo "✅ 镜像加载成功"
    else
        echo "❌ 镜像加载失败"
        exit 1
    fi

elif [ "$BUILD_MODE" = "local" ] || [ "$BUILD_MODE" = "build" ]; then
    # 构建模式：本地构建镜像
    echo "🔨 开始构建ARM64镜像..."
    echo "镜像名称: $IMAGE_NAME"
    echo "使用Dockerfile: Dockerfile.arm64"

    # 删除旧镜像（如果存在）
    if docker images | grep -q "paddleocr-service.*arm64"; then
        echo "🧹 清理旧镜像..."
        docker rmi "$IMAGE_NAME" >/dev/null 2>&1
        echo "✅ 旧镜像已清理"
    fi

    # 构建镜像
    if docker build -f Dockerfile.arm64 -t "$IMAGE_NAME" .; then
        echo "✅ 镜像构建成功"
    else
        echo "❌ 镜像构建失败"
        exit 1
    fi

    # 如果是仅构建模式，则退出
    if [ "$BUILD_MODE" = "build" ]; then
        echo "🎉 镜像构建完成！"
        echo "📦 导出镜像命令:"
        echo "  docker save $IMAGE_NAME -o paddleocr-service-arm64.tar"
        exit 0
    fi
fi

# 验证镜像
echo "🔍 验证镜像..."
if docker images | grep -q "paddleocr-service.*arm64"; then
    echo "✅ 镜像验证成功"
    docker images | grep paddleocr-service
else
    echo "❌ 镜像验证失败"
    exit 1
fi

# 创建日志目录
LOG_DIR="/var/log/paddleocr"
if [ ! -d "$LOG_DIR" ]; then
    echo "📁 创建日志目录: $LOG_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo chmod 755 "$LOG_DIR"
fi

# 启动容器
echo "🚀 启动PaddleOCR服务容器..."
docker run -d \
    --name "$CONTAINER_NAME" \
    -p "$PORT:$PORT" \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    -v "$LOG_DIR:/app/logs" \
    "$IMAGE_NAME"

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查容器状态
echo "🔍 检查容器状态..."
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ 容器运行正常"
    docker ps | grep "$CONTAINER_NAME"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..5}; do
    if curl -f "http://localhost:$PORT/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/5)"
        sleep 5
    fi
    
    if [ $i -eq 5 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi
done

# 显示服务信息
echo ""
echo "🎉 部署成功！"
echo "==============="
echo "服务名称: $CONTAINER_NAME"
echo "服务端口: $PORT"
echo "健康检查: http://localhost:$PORT/health"
echo "OCR接口: http://localhost:$PORT/ocr"
echo "日志目录: $LOG_DIR"
echo ""

# 显示测试命令
echo "📋 测试命令:"
echo "# 健康检查"
echo "curl http://localhost:$PORT/health"
echo ""
echo "# OCR测试（需要准备测试图片）"
echo "curl -X POST -F \"file=@test_image.png\" http://localhost:$PORT/ocr"
echo ""

# 显示管理命令
echo "🔧 管理命令:"
echo "# 查看容器状态"
echo "docker ps | grep $CONTAINER_NAME"
echo ""
echo "# 查看容器日志"
echo "docker logs $CONTAINER_NAME"
echo ""
echo "# 重启容器"
echo "docker restart $CONTAINER_NAME"
echo ""
echo "# 停止容器"
echo "docker stop $CONTAINER_NAME"
echo ""

echo "✅ 部署完成！"
