#!/bin/bash

# 后台守护进程方式启动PaddleOCR服务

echo "🚀 后台启动PaddleOCR服务"
echo "========================================="
echo

# 停止现有容器
echo "🧹 清理现有容器..."
docker stop paddleocr-offline 2>/dev/null || true
docker rm paddleocr-offline 2>/dev/null || true

# 启动基础容器
echo "📦 启动基础容器..."
docker run -d \
  --name paddleocr-offline \
  --privileged \
  --network=host \
  --shm-size=128G \
  -w=/work \
  -v $(pwd):/work \
  -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
  -v /usr/local/dcmi:/usr/local/dcmi \
  -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
  paddleocr-offline-ascend:latest \
  tail -f /dev/null

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 创建启动脚本
echo "📝 创建服务启动脚本..."
cat > /tmp/start_flask.sh << 'EOF'
#!/bin/bash
cd /work
export FLASK_ENV=development
export FLASK_DEBUG=1
nohup python -c "from app_simple import app; app.run(host='0.0.0.0', port=9527, debug=True)" > /tmp/flask.log 2>&1 &
echo $! > /tmp/flask.pid
echo "Flask服务已启动，PID: $(cat /tmp/flask.pid)"
echo "日志文件: /tmp/flask.log"
EOF

# 复制脚本到容器
docker cp /tmp/start_flask.sh paddleocr-offline:/tmp/
docker exec paddleocr-offline chmod +x /tmp/start_flask.sh

# 在容器内启动服务
echo "🔥 启动Flask服务..."
docker exec paddleocr-offline /tmp/start_flask.sh

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 测试服务
echo "🧪 测试服务..."
if curl -s http://localhost:9527/health >/dev/null 2>&1; then
    echo "✅ 服务启动成功!"
    echo "📍 服务地址: http://localhost:9527"
    echo "🔗 健康检查: http://localhost:9527/health"
    echo "🔗 OCR接口: http://localhost:9527/ocr (POST)"
    echo
    echo "📋 管理命令:"
    echo "  查看服务状态: docker exec paddleocr-offline ps aux | grep python"
    echo "  查看服务日志: docker exec paddleocr-offline tail -f /tmp/flask.log"
    echo "  停止服务: docker exec paddleocr-offline pkill -f 'python -c'"
    echo "  停止容器: docker stop paddleocr-offline"
    echo
    echo "🧪 测试OCR:"
    echo "  curl -X POST -F 'file=@kai.jpg' http://localhost:9527/ocr"
    echo
    echo "✨ 服务现在在后台运行，关闭终端不会影响服务！"
else
    echo "❌ 服务启动失败"
    echo "查看日志: docker exec paddleocr-offline cat /tmp/flask.log"
fi

# 清理临时文件
rm -f /tmp/start_flask.sh
