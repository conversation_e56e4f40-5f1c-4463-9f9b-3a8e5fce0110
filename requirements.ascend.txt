# 昇腾NPU版本依赖配置 - 用户指定版本组合
# 适用于昇腾910B + CANN 8.0.RC1

# 基本依赖
flask==3.0.0
werkzeug==3.0.1
numpy>=1.26.0
Pillow>=10.1.0
python-dotenv==1.0.0

# PDF处理
PyMuPDF>=1.23.0

# 昇腾NPU版本的PaddlePaddle - 用户指定版本组合
# paddlepaddle 3.1.0 + paddle-custom-npu 3.1.0 + paddleocr 3.0.0
# 使用最新版本组合，配合CANN 8.0.RC1
# 安装命令:
# pip install paddlepaddle==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/
# pip install paddle-custom-npu==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/npu/
paddlepaddle==3.1.0
paddle-custom-npu==3.1.0
paddleocr==3.0.0

# 昇腾设备支持
# paddle_custom_device 会随 paddle-custom-npu 一起安装
# 注意：确保CANN工具包版本为8.0.RC1以获得最佳兼容性
