#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PaddleOCR ARM64部署测试脚本
用于验证部署的PaddleOCR服务是否正常工作
"""

import os
import sys
import time
import json
import requests
from PIL import Image, ImageDraw, ImageFont

def create_test_image():
    """创建测试图片"""
    print("📝 创建测试图片...")
    
    # 创建一个简单的测试图片
    width, height = 400, 200
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # 添加测试文本
    test_texts = [
        "PaddleOCR测试",
        "ARM64部署验证",
        "Hello World",
        "2024年测试"
    ]
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 20)
    except:
        try:
            # 备用字体
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    y_offset = 30
    for text in test_texts:
        draw.text((20, y_offset), text, fill='black', font=font)
        y_offset += 40
    
    # 保存测试图片
    test_image_path = "test_deployment_image.png"
    image.save(test_image_path)
    print(f"✅ 测试图片已创建: {test_image_path}")
    
    return test_image_path

def test_health_check(base_url):
    """测试健康检查接口"""
    print("🏥 测试健康检查接口...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 健康检查请求失败: {e}")
        return False

def test_service_info(base_url):
    """测试服务信息接口"""
    print("ℹ️ 获取服务信息...")
    
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("✅ 服务信息获取成功:")
            print(f"   服务: {info.get('service', 'N/A')}")
            print(f"   版本: {info.get('version', 'N/A')}")
            print(f"   支持格式: {info.get('supported_formats', 'N/A')}")
            return True
        else:
            print(f"❌ 服务信息获取失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务信息请求失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ 服务信息解析失败: {e}")
        return False

def test_ocr_service(base_url, image_path):
    """测试OCR识别服务"""
    print("🔍 测试OCR识别服务...")
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/ocr", files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OCR识别成功")
            
            if 'texts' in result:
                texts = result['texts']
                print(f"   识别到 {len(texts)} 页内容:")
                for i, page_texts in enumerate(texts, 1):
                    print(f"   第{i}页: {page_texts}")
            elif 'error' in result:
                print(f"   OCR处理错误: {result['error']}")
                return False
            else:
                print(f"   未知响应格式: {result}")
                return False
            
            return True
        else:
            print(f"❌ OCR识别失败: HTTP {response.status_code}")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ OCR请求失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ OCR响应解析失败: {e}")
        return False

def test_performance(base_url, image_path, iterations=3):
    """测试性能"""
    print(f"⚡ 性能测试 ({iterations}次)...")
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False
    
    times = []
    success_count = 0
    
    for i in range(iterations):
        print(f"   执行第 {i+1}/{iterations} 次测试...")
        start_time = time.time()
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{base_url}/ocr", files=files, timeout=60)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                times.append(duration)
                success_count += 1
                print(f"   ✅ 第{i+1}次: {duration:.2f}秒")
            else:
                print(f"   ❌ 第{i+1}次: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 第{i+1}次: {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"📊 性能统计:")
        print(f"   成功率: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
        print(f"   平均耗时: {avg_time:.2f}秒")
        print(f"   最快耗时: {min_time:.2f}秒")
        print(f"   最慢耗时: {max_time:.2f}秒")
        
        return True
    else:
        print("❌ 所有性能测试都失败了")
        return False

def main():
    """主函数"""
    print("🚀 PaddleOCR ARM64部署测试")
    print("=" * 40)
    
    # 配置
    host = os.environ.get('PADDLEOCR_HOST', 'localhost')
    port = os.environ.get('PADDLEOCR_PORT', '9527')
    base_url = f"http://{host}:{port}"
    
    print(f"🎯 测试目标: {base_url}")
    print()
    
    # 创建测试图片
    test_image_path = create_test_image()
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 1. 健康检查测试
    total_tests += 1
    if test_health_check(base_url):
        passed_tests += 1
    print()
    
    # 2. 服务信息测试
    total_tests += 1
    if test_service_info(base_url):
        passed_tests += 1
    print()
    
    # 3. OCR功能测试
    total_tests += 1
    if test_ocr_service(base_url, test_image_path):
        passed_tests += 1
    print()
    
    # 4. 性能测试
    total_tests += 1
    if test_performance(base_url, test_image_path):
        passed_tests += 1
    print()
    
    # 清理测试文件
    try:
        os.remove(test_image_path)
        print(f"🧹 已清理测试图片: {test_image_path}")
    except:
        pass
    
    # 测试结果
    print("=" * 40)
    print(f"📋 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！PaddleOCR服务部署成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查服务状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
