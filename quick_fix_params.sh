#!/bin/bash

# 快速修复参数兼容性问题
# 修复PaddleOCR 3.0.0参数兼容性

echo "🔧 PaddleOCR 3.0.0 参数兼容性修复"
echo "==============================="

echo "📋 修复说明:"
echo "  问题: PaddleOCR 3.0.0移除了某些参数"
echo "  修复: 移除不兼容的参数，使用最简配置"
echo "  状态: 代码已修复，准备重新部署"
echo ""

# 检查Docker环境
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 停止当前容器
if docker ps | grep -q paddleocr-service; then
    echo "🛑 停止当前容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
    echo "✅ 容器已停止"
else
    echo "ℹ️ 没有运行中的容器"
fi

# 删除当前镜像
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    echo "🧹 删除当前镜像..."
    docker rmi paddleocr-service:arm64-latest
    echo "✅ 镜像已删除"
fi

# 重新构建镜像
echo ""
echo "🔨 重新构建镜像（修复参数兼容性）..."
echo "使用最简配置，确保兼容性"

start_time=$(date +%s)

if docker build -f Dockerfile.online -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 镜像重新构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 重新部署服务
echo ""
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 6g \
    --cpus 2 \
    --shm-size 1g \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查容器状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 检查启动日志
echo ""
echo "📋 检查启动日志..."
docker logs paddleocr-service --tail 20

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 验证修复效果
echo ""
echo "🔍 验证修复效果..."
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ 服务信息:')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
        print('✅ 参数兼容性问题已修复')
    else:
        print('❌ 服务响应异常')
except Exception as e:
    print(f'⚠️ 验证异常: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法验证服务信息，但服务可能仍在初始化中"
fi

echo ""
echo "🎉 参数兼容性修复完成！"
echo "======================="
echo "✅ 移除了不兼容的参数"
echo "✅ 使用最简配置确保稳定性"
echo "✅ 服务正常运行"
echo ""
echo "📋 当前配置:"
echo "   use_angle_cls: True"
echo "   lang: ch"
echo "   enable_mkldnn: False"
echo "   其他参数: 使用默认值"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   curl http://localhost:9527/health"
echo ""
echo "🔧 如果仍有问题:"
echo "   查看日志: docker logs paddleocr-service"
echo "   重启服务: docker restart paddleocr-service"
echo ""

echo "✅ 修复流程完成！"
