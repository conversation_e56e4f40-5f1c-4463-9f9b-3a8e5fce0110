#!/bin/bash

# 昇腾环境诊断脚本
# 用于检查昇腾NPU环境是否完整

echo "========================================="
echo "昇腾NPU环境诊断脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "🔍 系统架构: $ARCH"
if [ "$ARCH" != "aarch64" ]; then
    echo "⚠️  警告: 当前系统不是ARM64架构"
fi
echo

# 检查昇腾目录结构
echo "🔍 检查昇腾目录结构..."
ASCEND_DIRS=(
    "/usr/local/Ascend"
    "/usr/local/Ascend/driver"
    "/usr/local/Ascend/driver/lib64"
    "/usr/local/Ascend/ascend-toolkit"
    "/usr/local/Ascend/ascend-toolkit/latest"
    "/usr/local/Ascend/ascend-toolkit/latest/lib64"
)

for dir in "${ASCEND_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ 目录存在: $dir"
        # 显示目录内容（前5个）
        echo "   内容: $(ls -1 "$dir" 2>/dev/null | head -3 | tr '\n' ' ')..."
    else
        echo "❌ 目录不存在: $dir"
    fi
done
echo

# 检查关键库文件
echo "🔍 检查关键库文件..."
CRITICAL_LIBS=(
    "/usr/local/Ascend/driver/lib64/libmsprofiler.so"
    "/usr/local/Ascend/ascend-toolkit/latest/lib64/libmsprofiler.so"
    "/usr/local/Ascend/driver/lib64/libacl.so"
    "/usr/local/Ascend/driver/lib64/libge_runner.so"
    "/usr/local/Ascend/driver/lib64/libgraph.so"
)

LIBS_FOUND=0
for lib in "${CRITICAL_LIBS[@]}"; do
    if [ -f "$lib" ]; then
        echo "✅ 库文件存在: $lib"
        LIBS_FOUND=$((LIBS_FOUND + 1))
    else
        echo "❌ 库文件不存在: $lib"
    fi
done

if [ $LIBS_FOUND -eq 0 ]; then
    echo "⚠️  警告: 未找到任何关键库文件"
else
    echo "✅ 找到 $LIBS_FOUND 个关键库文件"
fi
echo

# 检查工具和命令
echo "🔍 检查昇腾工具..."
TOOLS=(
    "/usr/local/bin/npu-smi"
    "/usr/local/dcmi"
)

for tool in "${TOOLS[@]}"; do
    if [ -e "$tool" ]; then
        echo "✅ 工具存在: $tool"
    else
        echo "❌ 工具不存在: $tool"
    fi
done

# 检查npu-smi命令
if command -v npu-smi &> /dev/null; then
    echo "✅ npu-smi命令可用"
    echo "   尝试执行npu-smi info..."
    if npu-smi info >/dev/null 2>&1; then
        echo "✅ npu-smi info执行成功"
        npu-smi info | head -10
    else
        echo "⚠️  npu-smi info执行失败"
    fi
else
    echo "❌ npu-smi命令不可用"
fi
echo

# 检查环境变量
echo "🔍 检查昇腾相关环境变量..."
ENV_VARS=(
    "LD_LIBRARY_PATH"
    "PATH"
    "ASCEND_OPP_PATH"
    "ASCEND_AICPU_PATH"
    "ASCEND_TOOLKIT_HOME"
)

for var in "${ENV_VARS[@]}"; do
    if [ -n "${!var}" ]; then
        echo "✅ $var = ${!var}"
    else
        echo "⚠️  $var 未设置"
    fi
done
echo

# 搜索libmsprofiler.so文件
echo "🔍 搜索libmsprofiler.so文件..."
MSPROFILER_PATHS=$(find /usr/local -name "libmsprofiler.so*" 2>/dev/null)
if [ -n "$MSPROFILER_PATHS" ]; then
    echo "✅ 找到libmsprofiler.so文件:"
    echo "$MSPROFILER_PATHS"
else
    echo "❌ 未找到libmsprofiler.so文件"
    echo "   这可能是导致paddle导入失败的原因"
fi
echo

# 检查Python环境
echo "🔍 检查Python环境..."
if command -v python3 &> /dev/null; then
    echo "✅ Python3版本: $(python3 --version)"
    
    # 检查paddle相关包
    echo "🔍 检查已安装的paddle相关包..."
    python3 -c "
import subprocess
import sys
try:
    result = subprocess.run([sys.executable, '-m', 'pip', 'list'], capture_output=True, text=True)
    lines = result.stdout.split('\n')
    paddle_packages = [line for line in lines if 'paddle' in line.lower()]
    if paddle_packages:
        print('✅ 已安装的paddle相关包:')
        for pkg in paddle_packages:
            print(f'   {pkg}')
    else:
        print('❌ 未找到paddle相关包')
except Exception as e:
    print(f'❌ 检查包时出错: {e}')
"
else
    echo "❌ Python3不可用"
fi
echo

# 生成诊断报告
echo "========================================="
echo "诊断报告总结"
echo "========================================="

# 计算环境完整性得分
SCORE=0
MAX_SCORE=10

# 检查关键目录
if [ -d "/usr/local/Ascend" ]; then SCORE=$((SCORE + 2)); fi
if [ -d "/usr/local/Ascend/driver" ]; then SCORE=$((SCORE + 2)); fi

# 检查关键库文件
if [ $LIBS_FOUND -gt 0 ]; then SCORE=$((SCORE + 3)); fi

# 检查工具
if [ -f "/usr/local/bin/npu-smi" ]; then SCORE=$((SCORE + 1)); fi
if command -v npu-smi &> /dev/null; then SCORE=$((SCORE + 1)); fi

# 检查libmsprofiler.so
if [ -n "$MSPROFILER_PATHS" ]; then SCORE=$((SCORE + 1)); fi

echo "环境完整性得分: $SCORE/$MAX_SCORE"

if [ $SCORE -ge 8 ]; then
    echo "✅ 昇腾环境看起来比较完整，应该可以正常使用NPU"
elif [ $SCORE -ge 5 ]; then
    echo "⚠️  昇腾环境部分完整，可能需要额外配置"
else
    echo "❌ 昇腾环境不完整，建议检查驱动安装"
fi

echo
echo "🔧 建议的Docker挂载命令:"
echo "docker run -d \\"
echo "  --name paddleocr-official-ascend \\"
echo "  --privileged --network=host --shm-size=128G \\"
echo "  -v \$(pwd):/work -w=/work \\"
echo "  -v /usr/local/Ascend:/usr/local/Ascend \\"
if [ -f "/usr/local/bin/npu-smi" ]; then
    echo "  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
fi
if [ -d "/usr/local/dcmi" ]; then
    echo "  -v /usr/local/dcmi:/usr/local/dcmi \\"
fi
echo "  -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
echo "  paddleocr-official-ascend:latest"
