[Unit]
Description=PaddleOCR NPU Service
After=docker.service
Requires=docker.service

[Service]
Type=forking
User=root
WorkingDirectory=/opt/env-paddle
ExecStartPre=-/usr/bin/docker stop paddleocr-offline
ExecStartPre=-/usr/bin/docker rm paddleocr-offline
ExecStart=/opt/env-paddle/daemon_start.sh
ExecStop=/usr/bin/docker stop paddleocr-offline
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
