# PaddleOCR ARM64架构部署完整方案

## 概述

本方案提供了在ARM64架构机器上构建、打包和部署PaddleOCR Docker容器的完整流程，支持离线环境部署，无需在运行时下载模型。

## 前置条件

### Windows构建环境要求
- Windows 10/11 (x64)
- Docker Desktop with BuildKit support
- 已启用 `docker buildx` 多架构构建支持

### ARM64目标环境要求
- ARM64架构Linux服务器
- Docker Engine 已安装
- 无网络连接环境（离线部署）

## 文件准备

### 1. 模型文件准备
确保项目根目录包含以下模型文件：
```
paddlex_models.tar.gz  # 包含以下模型：
├── PP-LCNet_x1_0_doc_ori
├── PP-LCNet_x1_0_textline_ori
├── PP-OCRv5_server_det
├── PP-OCRv5_server_rec
└── UVDoc
```

### 2. 项目文件结构
```
paddleocr-docker/
├── Dockerfile.arm64           # ARM64专用Dockerfile
├── build_arm64.bat           # Windows构建脚本
├── build_arm64.sh            # Linux构建脚本
├── paddlex_models.tar.gz     # 预训练模型文件
├── requirements.txt          # Python依赖
├── app.py                    # Flask应用
├── ocr_processor.py          # OCR处理器
├── run.py                    # 启动脚本
└── 其他项目文件...
```

## 构建流程

### 在Windows环境构建ARM64镜像

#### 方法1: 使用批处理脚本（推荐）
```cmd
# 运行构建脚本
build_arm64.bat
```

#### 方法2: 手动构建
```cmd
# 检查Docker BuildKit支持
docker buildx version

# 创建多架构构建器（如果不存在）
docker buildx create --name multiarch --use

# 构建ARM64镜像
docker buildx build --platform linux/arm64 -f Dockerfile.arm64 -t paddleocr-service:arm64-latest .

# 导出镜像为tar文件
docker save paddleocr-service:arm64-latest -o paddleocr-service-arm64.tar
```

### 在Linux环境构建ARM64镜像

```bash
# 给脚本执行权限
chmod +x build_arm64.sh

# 运行构建脚本
./build_arm64.sh
```

## 部署流程

### 1. 传输镜像到ARM64服务器

```bash
# 方法1: 使用scp传输
scp paddleocr-service-arm64.tar user@arm64-server:/tmp/

# 方法2: 使用U盘等物理介质传输（离线环境）
```

### 2. 在ARM64服务器上加载镜像

```bash
# 加载Docker镜像
docker load -i /tmp/paddleocr-service-arm64.tar

# 验证镜像加载成功
docker images | grep paddleocr-service
```

### 3. 启动容器

#### 基本启动
```bash
docker run -d \
  --name paddleocr-service \
  -p 9527:9527 \
  paddleocr-service:arm64-latest
```

#### 生产环境启动（推荐）
```bash
docker run -d \
  --name paddleocr-service \
  -p 9527:9527 \
  --restart unless-stopped \
  --memory 4g \
  --cpus 2 \
  -v /var/log/paddleocr:/app/logs \
  paddleocr-service:arm64-latest
```

### 4. 验证部署

```bash
# 检查容器状态
docker ps | grep paddleocr-service

# 检查容器日志
docker logs paddleocr-service

# 测试健康检查接口
curl http://localhost:9527/health

# 测试OCR接口
curl -X POST -F "file=@test_image.png" http://localhost:9527/ocr
```

## 特性说明

### 模型预加载机制
- 构建时将模型文件解压到 `/root/.paddleocr/` 目录
- 设置 `PADDLEOCR_HOME` 环境变量指向预加载目录
- 容器启动时直接使用预加载模型，无需下载

### 性能优化
- 使用Python 3.12-slim基础镜像，减小镜像体积
- 预安装所有依赖，加快容器启动速度
- 支持NPU加速（如果ARM64服务器支持）

### 离线部署支持
- 所有模型文件预打包在镜像中
- 无需外网连接即可正常运行
- 支持完全离线环境部署

## 故障排除

### 1. 构建失败
```bash
# 检查Docker BuildKit是否启用
docker buildx version

# 检查模型文件是否存在
ls -la paddlex_models.tar.gz

# 清理构建缓存重试
docker buildx prune -f
```

### 2. 模型加载失败
```bash
# 检查容器内模型目录
docker exec paddleocr-service ls -la /root/.paddleocr/

# 检查环境变量
docker exec paddleocr-service env | grep PADDLE
```

### 3. 性能问题
```bash
# 增加内存限制
docker update --memory 8g paddleocr-service

# 检查CPU使用情况
docker stats paddleocr-service
```

## 监控和维护

### 日志管理
```bash
# 查看实时日志
docker logs -f paddleocr-service

# 限制日志大小
docker run --log-opt max-size=100m --log-opt max-file=3 ...
```

### 健康检查
```bash
# 定期健康检查脚本
#!/bin/bash
if ! curl -f http://localhost:9527/health; then
    echo "Service unhealthy, restarting..."
    docker restart paddleocr-service
fi
```

### 备份和恢复
```bash
# 备份镜像
docker save paddleocr-service:arm64-latest | gzip > paddleocr-backup.tar.gz

# 恢复镜像
gunzip -c paddleocr-backup.tar.gz | docker load
```

## 总结

本方案提供了完整的ARM64架构PaddleOCR部署解决方案，支持：
- ✅ 离线环境部署
- ✅ 模型预加载，无需运行时下载
- ✅ 跨平台构建（Windows构建，ARM64部署）
- ✅ 生产环境优化配置
- ✅ 完整的故障排除指南

按照本指南操作，可以在ARM64服务器上成功部署PaddleOCR服务，并在无网络环境下正常运行。
