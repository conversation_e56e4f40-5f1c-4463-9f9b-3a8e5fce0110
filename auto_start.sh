#!/bin/bash

# 自动启动PaddleOCR服务脚本
# 使用交互式方式确保服务正常启动

echo "🚀 自动启动PaddleOCR服务"
echo "========================================="
echo

# 停止现有容器
echo "🧹 清理现有容器..."
docker stop paddleocr-offline 2>/dev/null || true
docker rm paddleocr-offline 2>/dev/null || true

# 启动容器并直接进入交互模式
echo "📦 启动容器并进入交互模式..."
echo "容器启动后，请运行以下命令启动服务:"
echo
echo "cd /work"
echo "export FLASK_ENV=development"
echo "export FLASK_DEBUG=1"
echo "python -c \"from app_simple import app; app.run(host='0.0.0.0', port=9527, debug=True)\""
echo
echo "按任意键继续..."
read -n 1 -s

docker run -it \
  --name paddleocr-offline \
  --privileged \
  --network=host \
  --shm-size=128G \
  -w=/work \
  -v $(pwd):/work \
  -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
  -v /usr/local/dcmi:/usr/local/dcmi \
  -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
  paddleocr-offline-ascend:latest \
  bash
