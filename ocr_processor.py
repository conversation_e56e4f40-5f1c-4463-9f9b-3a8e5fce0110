import os
import json
import tempfile
from paddleocr import PaddleOCR

class OCRProcessor:
    def __init__(self):
        """初始化PaddleOCR"""
        print("🚀 初始化PaddleOCR处理器...")
        print("📦 PaddleOCR将自动从网络下载所需模型")

        # 使用PaddleOCR 3.1.0的预测功能
        # 使用中文和英文模型，可以根据需要调整
        try:
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang="ch",
                enable_mkldnn=True
            )
            print("✅ PaddleOCR初始化成功")
        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            raise e



    def process(self, file_path):
        """
        处理上传的文件并进行OCR识别

        Args:
            file_path: 上传文件的路径

        Returns:
            OCR识别结果的JSON格式
        """
        file_ext = os.path.splitext(file_path)[1].lower()

        # 检查文件类型
        if file_ext not in ['.pdf', '.png', '.jpg', '.jpeg']:
            raise ValueError(f"不支持的文件类型: {file_ext}")

        try:
            print(f"开始处理文件: {file_path}")

            # 直接使用PaddleOCR的predict方法处理文件
            # 这与官方文档的处理方式一致
            result = self.ocr.predict(input=file_path)
            print(f"OCR处理完成，结果类型: {type(result)}")

            # 如果结果为空
            if not result:
                print("OCR结果为空")
                return []

            # 只提取并返回rec_texts内容
            text_results = []
            page_num = 0

            # 遍历结果，每个res对应一页
            for res in result:
                page_num += 1
                print(f"处理第 {page_num} 页结果")

                try:
                    # 使用save_to_json方法获取JSON格式的结果
                    # 创建临时目录来保存JSON文件
                    with tempfile.TemporaryDirectory() as temp_dir:
                        json_path = os.path.join(temp_dir, f"temp_result_{page_num}.json")
                        res.save_to_json(json_path)

                        # 读取JSON文件
                        with open(json_path, 'r', encoding='utf-8') as f:
                            page_result = json.load(f)
                            print(f"JSON结果的键: {list(page_result.keys())}")

                            # 检查是否存在rec_texts字段
                            if 'rec_texts' in page_result:
                                text_results.append(page_result['rec_texts'])
                            elif 'text_regions' in page_result:
                                # 如果没有rec_texts字段，尝试从每个文本框中提取文本
                                texts = []
                                for region in page_result['text_regions']:
                                    if 'text' in region:
                                        texts.append(region['text'])
                                text_results.append(texts)
                            else:
                                # 如果上述字段都不存在，尝试直接提取文本
                                # 这是一个后备方案，尝试处理不同的结果格式
                                try:
                                    # 尝试直接使用res.text获取文本
                                    if hasattr(res, 'text') and res.text:
                                        text_results.append([res.text])
                                    else:
                                        # 如果所有方法都失败，添加空列表
                                        text_results.append([])
                                except Exception as e:
                                    print(f"提取文本时出错: {str(e)}")
                                    text_results.append([])
                except Exception as e:
                    print(f"处理页面 {page_num} 时出错: {str(e)}")
                    text_results.append([])

            # 如果没有结果，返回空列表
            if not text_results:
                return []

            print(f"共提取了 {len(text_results)} 页的文本内容")

            return text_results

        except Exception as e:
            print(f"处理文件时出错: {str(e)}")
            # 返回错误信息
            return [{
                "error": f"处理文件时出错: {str(e)}"
            }]


