#!/bin/bash

# 手动启动PaddleOCR服务脚本
# 这个脚本会启动容器，然后提供手动启动服务的指令

echo "🚀 手动启动PaddleOCR服务"
echo "========================================="
echo

# 停止现有容器
echo "🧹 清理现有容器..."
docker stop paddleocr-offline 2>/dev/null || true
docker rm paddleocr-offline 2>/dev/null || true

# 启动基础容器
echo "📦 启动基础容器..."
docker run -d \
  --name paddleocr-offline \
  --privileged \
  --network=host \
  --shm-size=128G \
  -w=/work \
  -v $(pwd):/work \
  -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
  -v /usr/local/dcmi:/usr/local/dcmi \
  -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
  paddleocr-offline-ascend:latest \
  tail -f /dev/null

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

if docker ps | grep -q paddleocr-offline; then
    echo "✅ 容器启动成功!"
    echo
    echo "📋 现在请按照以下步骤手动启动服务:"
    echo
    echo "1️⃣  进入容器:"
    echo "   docker exec -it paddleocr-offline bash"
    echo
    echo "2️⃣  在容器内运行以下命令:"
    echo "   cd /work"
    echo "   export FLASK_ENV=development"
    echo "   export FLASK_DEBUG=1"
    echo "   python -c \"from app_simple import app; app.run(host='0.0.0.0', port=9527, debug=True)\""
    echo
    echo "🔥 或者直接复制粘贴这个一行命令:"
    echo "   docker exec -it paddleocr-offline bash -c \"cd /work && export FLASK_ENV=development && export FLASK_DEBUG=1 && python -c 'from app_simple import app; app.run(host=\\\"0.0.0.0\\\", port=9527, debug=True)'\""
    echo
    echo "3️⃣  服务启动后，在另一个终端测试:"
    echo "   curl http://localhost:9527/health"
    echo "   curl -X POST -F 'file=@kai.jpg' http://localhost:9527/ocr"
    echo
    echo "📝 管理命令:"
    echo "   停止容器: docker stop paddleocr-offline"
    echo "   查看日志: docker logs paddleocr-offline"
    echo
else
    echo "❌ 容器启动失败"
    echo "请检查Docker状态和权限"
fi
