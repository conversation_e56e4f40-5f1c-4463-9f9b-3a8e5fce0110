#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PaddleOCR项目完整性验证脚本
检查项目文件的完整性和配置的正确性
"""

import os
import sys
import json
from pathlib import Path

def check_file_exists(filepath, description=""):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✓ {filepath} ({size} bytes) {description}")
        return True
    else:
        print(f"❌ {filepath} - 文件不存在 {description}")
        return False

def check_directory_structure():
    """检查项目目录结构"""
    print("=" * 50)
    print("检查项目目录结构")
    print("=" * 50)
    
    required_files = {
        # 核心应用文件
        "app.py": "Flask应用主文件",
        "ocr_processor.py": "OCR处理模块",
        "run.py": "服务启动脚本",
        "load_env.py": "环境变量加载",
        ".env": "环境配置文件",
        
        # 依赖配置
        "requirements.txt": "GPU版本依赖",
        "requirements.ascend.txt": "昇腾NPU版本依赖",
        
        # Docker配置
        "Dockerfile": "GPU版本Dockerfile",
        "Dockerfile.ascend": "昇腾NPU版本Dockerfile",
        "docker-compose.yml": "GPU版本Docker Compose",
        "docker-compose.ascend.yml": "昇腾NPU版本Docker Compose",
        
        # 构建脚本
        "build_ascend_docker.sh": "Linux昇腾构建脚本",
        "build_ascend_docker.bat": "Windows昇腾构建脚本",
        "build_arm64_server.sh": "ARM64服务器构建脚本",
        
        # 部署脚本
        "run_container.sh": "容器运行脚本",
        "server_setup.sh": "服务器环境配置脚本",
        "quick_deploy.sh": "一键部署脚本",
        "package_for_deployment.bat": "Windows打包脚本",
        
        # 文档
        "README.md": "项目说明文档",
        "deploy_guide.md": "完整部署指南",
        "troubleshooting.md": "故障排除指南",
        
        # 测试文件
        "test_ocr.py": "OCR测试脚本",
        "test_client.py": "客户端测试脚本",
        "create_test_image.py": "测试图片生成脚本",
        "verify_project.py": "项目验证脚本"
    }
    
    missing_files = []
    total_files = len(required_files)
    found_files = 0
    
    for filepath, description in required_files.items():
        if check_file_exists(filepath, f"- {description}"):
            found_files += 1
        else:
            missing_files.append(filepath)
    
    print(f"\n文件检查结果: {found_files}/{total_files} 个文件存在")
    
    if missing_files:
        print(f"\n缺失的文件:")
        for file in missing_files:
            print(f"  - {file}")
    
    return len(missing_files) == 0

def check_docker_configs():
    """检查Docker配置文件"""
    print("\n" + "=" * 50)
    print("检查Docker配置")
    print("=" * 50)
    
    configs_ok = True
    
    # 检查Dockerfile.ascend
    if os.path.exists("Dockerfile.ascend"):
        with open("Dockerfile.ascend", "r", encoding="utf-8") as f:
            content = f.read()
            if "FROM ubuntu:22.04" in content:
                print("✓ Dockerfile.ascend - 使用正确的基础镜像")
            else:
                print("❌ Dockerfile.ascend - 基础镜像配置可能有问题")
                configs_ok = False
                
            if "paddle-custom-npu==3.0.0" in content:
                print("✓ Dockerfile.ascend - 包含昇腾NPU版本PaddlePaddle")
            else:
                print("❌ Dockerfile.ascend - 缺少昇腾NPU版本PaddlePaddle")
                configs_ok = False
    
    # 检查requirements.ascend.txt
    if os.path.exists("requirements.ascend.txt"):
        with open("requirements.ascend.txt", "r", encoding="utf-8") as f:
            content = f.read()
            if "paddle-custom-npu==3.0.0" in content:
                print("✓ requirements.ascend.txt - 包含昇腾NPU依赖")
            else:
                print("❌ requirements.ascend.txt - 缺少昇腾NPU依赖")
                configs_ok = False
    
    # 检查docker-compose.ascend.yml
    if os.path.exists("docker-compose.ascend.yml"):
        with open("docker-compose.ascend.yml", "r", encoding="utf-8") as f:
            content = f.read()
            if "/dev/davinci0" in content:
                print("✓ docker-compose.ascend.yml - 包含昇腾设备挂载")
            else:
                print("❌ docker-compose.ascend.yml - 缺少昇腾设备挂载")
                configs_ok = False
    
    return configs_ok

def check_script_permissions():
    """检查脚本文件权限（Linux/Unix系统）"""
    print("\n" + "=" * 50)
    print("检查脚本文件")
    print("=" * 50)
    
    script_files = [
        "build_ascend_docker.sh",
        "build_arm64_server.sh",
        "run_container.sh",
        "server_setup.sh",
        "quick_deploy.sh"
    ]
    
    scripts_ok = True
    
    for script in script_files:
        if os.path.exists(script):
            # 检查文件内容
            with open(script, "r", encoding="utf-8") as f:
                content = f.read()
                if content.startswith("#!/bin/bash"):
                    print(f"✓ {script} - 正确的bash脚本格式")
                else:
                    print(f"❌ {script} - 缺少bash shebang")
                    scripts_ok = False
        else:
            print(f"❌ {script} - 文件不存在")
            scripts_ok = False
    
    return scripts_ok

def check_python_syntax():
    """检查Python文件语法"""
    print("\n" + "=" * 50)
    print("检查Python文件语法")
    print("=" * 50)
    
    python_files = [
        "app.py",
        "ocr_processor.py",
        "run.py",
        "load_env.py",
        "test_ocr.py",
        "test_client.py",
        "create_test_image.py"
    ]
    
    syntax_ok = True
    
    for py_file in python_files:
        if os.path.exists(py_file):
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()
                    compile(content, py_file, "exec")
                print(f"✓ {py_file} - 语法正确")
            except SyntaxError as e:
                print(f"❌ {py_file} - 语法错误: {e}")
                syntax_ok = False
            except Exception as e:
                print(f"⚠️  {py_file} - 检查时出错: {e}")
        else:
            print(f"❌ {py_file} - 文件不存在")
            syntax_ok = False
    
    return syntax_ok

def check_project_configuration():
    """检查项目配置"""
    print("\n" + "=" * 50)
    print("检查项目配置")
    print("=" * 50)
    
    config_ok = True
    
    # 检查.env文件
    if os.path.exists(".env"):
        with open(".env", "r", encoding="utf-8") as f:
            content = f.read()
            if "FLASK_RUN_PORT=9527" in content:
                print("✓ .env - 端口配置正确")
            else:
                print("❌ .env - 端口配置可能有问题")
                config_ok = False
    
    # 检查app.py中的端口配置
    if os.path.exists("app.py"):
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
            if "9527" in content:
                print("✓ app.py - 包含端口配置")
            else:
                print("⚠️  app.py - 未找到端口配置")
    
    return config_ok

def generate_deployment_checklist():
    """生成部署检查清单"""
    print("\n" + "=" * 50)
    print("生成部署检查清单")
    print("=" * 50)
    
    checklist = """
# PaddleOCR 昇腾NPU 部署检查清单

## 开发环境准备 (Windows)
- [ ] Docker Desktop 已安装并运行
- [ ] 启用Docker Desktop实验性功能
- [ ] 项目文件完整性检查通过
- [ ] 运行 build_ascend_docker.bat 构建ARM64镜像
- [ ] 运行 package_for_deployment.bat 打包项目

## 服务器环境准备 (ARM64)
- [ ] 系统架构为aarch64
- [ ] 昇腾NPU硬件已安装
- [ ] 昇腾驱动已安装
- [ ] CANN工具包已安装
- [ ] Docker已安装并运行
- [ ] 网络连接正常

## 部署步骤
- [ ] 上传部署包到服务器
- [ ] 解压部署包
- [ ] 运行 sudo ./server_setup.sh 配置环境
- [ ] 运行 ./quick_deploy.sh 一键部署
- [ ] 验证服务: curl http://localhost:9527/health

## 验证测试
- [ ] 健康检查通过
- [ ] OCR功能测试通过
- [ ] 昇腾NPU设备检测正常
- [ ] 容器日志无错误

## 监控维护
- [ ] 配置日志轮转
- [ ] 设置服务监控
- [ ] 配置自动重启
- [ ] 定期备份配置
"""
    
    with open("deployment_checklist.md", "w", encoding="utf-8") as f:
        f.write(checklist)
    
    print("✓ 部署检查清单已生成: deployment_checklist.md")

def main():
    """主函数"""
    print("PaddleOCR 项目完整性验证")
    print("=" * 50)
    print(f"当前目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 执行各项检查
    checks = [
        ("目录结构", check_directory_structure),
        ("Docker配置", check_docker_configs),
        ("脚本文件", check_script_permissions),
        ("Python语法", check_python_syntax),
        ("项目配置", check_project_configuration)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name}检查时出错: {e}")
            results[check_name] = False
    
    # 生成部署检查清单
    generate_deployment_checklist()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("验证结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 项目验证完全通过！可以开始部署。")
        print("\n后续步骤:")
        print("1. Windows环境: 运行 package_for_deployment.bat")
        print("2. 传输到ARM64服务器")
        print("3. 运行 sudo ./quick_deploy.sh")
        return 0
    else:
        print(f"\n⚠️  发现 {total - passed} 个问题，请修复后重新验证。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
