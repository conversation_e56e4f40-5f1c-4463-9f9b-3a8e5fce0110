import os
import tempfile
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from ocr_processor import OCRProcessor

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 限制上传文件大小为1024MB
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()  # 使用系统临时目录

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg'}

# 初始化OCR处理器
# 可以通过环境变量控制是否使用PP-StructureV3
use_structure = os.getenv('USE_STRUCTURE', 'false').lower() == 'true'
ocr_processor = OCRProcessor(use_structure=use_structure)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/ocr', methods=['POST'])
def ocr_recognition():
    """OCR识别接口"""
    # 检查是否有文件上传
    if 'file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400

    file = request.files['file']

    # 检查文件名是否为空
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400

    # 检查文件类型是否允许
    if not allowed_file(file.filename):
        return jsonify({'error': f'不支持的文件类型，仅支持 {", ".join(ALLOWED_EXTENSIONS)}'}), 400

    try:
        # 保存上传的文件到临时目录
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        try:
            # 进行OCR识别
            print(f"开始处理文件: {filename}")
            result = ocr_processor.process(file_path)
            print(f"处理完成，结果类型: {type(result)}")

            # 检查结果是否有效
            if result is None or len(result) == 0:
                return jsonify({
                    'result': [],
                    'warning': '未检测到文本内容'
                })

            # 检查是否有错误
            if isinstance(result, list) and len(result) > 0 and "error" in result[0]:
                return jsonify({
                    'error': result[0]["error"]
                })

            # 返回提取的文本内容
            return jsonify({
                'texts': result
            })

        except Exception as e:
            print(f"OCR处理异常: {str(e)}")
            # 返回错误信息，但不返回500状态码
            return jsonify({
                'result': [],
                'error': f'OCR处理失败: {str(e)}'
            })

        finally:
            # 无论如何都删除临时文件
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"删除临时文件: {file_path}")
                except Exception as e:
                    print(f"删除临时文件失败: {str(e)}")

    except Exception as e:
        print(f"文件处理异常: {str(e)}")
        return jsonify({'error': f'文件处理失败: {str(e)}'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({'status': 'ok'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9527, debug=True)
