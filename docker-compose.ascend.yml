version: '3.8'

# 昇腾NPU版本Docker Compose配置
# 使用用户指定的版本组合：
# - paddlepaddle: 3.1.0
# - paddle-custom-npu: 3.1.0
# - paddleocr: 3.0.0
# - CANN: 8.0.RC1

services:
  paddleocr-ascend:
    build:
      context: .
      dockerfile: Dockerfile.ascend
    container_name: paddleocr-ascend-service
    ports:
      - "9527:9527"
    volumes:
      - ./:/app
      # 挂载昇腾设备和驱动库
      - /usr/local/Ascend:/usr/local/Ascend:ro
      - /var/log/npu:/var/log/npu
      # 挂载系统库文件以确保驱动兼容性
      - /lib/modules:/lib/modules:ro
      - /usr/lib/aarch64-linux-gnu:/usr/lib/aarch64-linux-gnu:ro
    devices:
      # 昇腾NPU设备（根据实际检测到的设备配置）
      - /dev/davinci4:/dev/davinci4  # 检测到的第一个NPU设备
      - /dev/davinci5:/dev/davinci5  # 检测到的第二个NPU设备

      # 昇腾管理设备（通常固定）
      - /dev/davinci_manager:/dev/davinci_manager
      - /dev/devmm_svm:/dev/devmm_svm
      - /dev/hisi_hdc:/dev/hisi_hdc
    environment:
      - FLAGS_selected_npus=4  # 使用检测到的第一个NPU设备ID
      - ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp
      - ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest
      - ASCEND_TOOLKIT_HOME=/usr/local/Ascend/ascend-toolkit/latest
      # NPU初始化相关环境变量
      - ASCEND_RT_VISIBLE_DEVICES=4,5  # 指定可见的NPU设备
      - FLAGS_init_allocated_mem=false  # 避免内存初始化问题
      - FLAGS_enable_eager_mode=1  # 启用动态图模式
      - FLAGS_use_mkldnn=0  # 禁用MKLDNN以避免冲突
      # 内存管理优化
      - FLAGS_fraction_of_gpu_memory_to_use=0.3
      - FLAGS_initial_gpu_memory_in_mb=512
      - FLAGS_reallocate_gpu_memory_in_mb=256
      - ASCEND_HOME_PATH=/usr/local/Ascend
      - ASCEND_NNAE_HOME=/usr/local/Ascend/nnae/latest
      - ASCEND_RT_VISIBLE_DEVICES=4,5  # 关键：设置检测到的NPU设备
      - LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64:/usr/local/Ascend/driver/lib64:/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/common
      - PATH=/usr/local/Ascend/ascend-toolkit/latest/bin:/usr/local/Ascend/ascend-toolkit/latest/atc/bin:$PATH
      - FLAGS_selected_npus=4  # 使用检测到的第一个NPU设备（设备ID为4）
      - FLAGS_allocator_strategy=auto_growth
      # NPU初始化优化环境变量
      - FLAGS_init_allocated_mem=false
      - FLAGS_enable_eager_mode=1
      - FLAGS_use_mkldnn=0
      # 驱动兼容性环境变量
      - ASCEND_DEVICE_ID=4
      - RANK_TABLE_FILE=""
      - ASCEND_GLOBAL_LOG_LEVEL=1
      - ASCEND_SLOG_PRINT_TO_STDOUT=1
    restart: unless-stopped
    privileged: true
    network_mode: host
    # 安全配置以支持NPU操作
    security_opt:
      - seccomp:unconfined
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9527/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # 增加启动等待时间，NPU初始化需要更长时间
