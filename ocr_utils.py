#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR处理工具类
专门用于官方容器内的OCR处理
"""

import os
import json
import time
import traceback

class OCRProcessor:
    """OCR处理器 - 官方容器版本"""
    
    def __init__(self):
        """初始化OCR处理器"""
        self.ocr = None
        self.paddle = None
        self.device_info = "未知"
        self.ready = False
        
        print("🔧 开始初始化PaddleOCR...")
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化PaddleOCR"""
        try:
            # 导入PaddlePaddle和PaddleOCR
            print("📦 导入PaddlePaddle和PaddleOCR...")
            import paddle
            from paddleocr import PaddleOCR
            
            self.paddle = paddle
            print("✅ 成功导入paddle和PaddleOCR")
            
            # 检测可用设备
            device_info = self._detect_device()
            print(f"🖥️ 检测到设备: {device_info}")
            
            # 设置环境变量
            self._setup_environment()
            
            # 初始化PaddleOCR
            print("🚀 初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang='ch'
            )
            
            self.ready = True
            print("✅ PaddleOCR初始化成功")
            
        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            print(f"异常详情: {traceback.format_exc()}")
            self.ready = False
            raise e
    
    def _detect_device(self):
        """检测可用设备"""
        try:
            # 检查NPU
            if self.paddle:
                try:
                    available_devices = self.paddle.device.get_all_custom_device_type()
                    if 'npu' in available_devices:
                        self.device_info = "NPU"
                        print("🚀 检测到NPU设备")
                        return "NPU"
                except:
                    pass
                
                # 检查GPU
                try:
                    if self.paddle.device.is_compiled_with_cuda():
                        gpu_count = self.paddle.device.cuda.device_count()
                        if gpu_count > 0:
                            self.device_info = f"GPU (数量: {gpu_count})"
                            print(f"🖥️ 检测到GPU设备，数量: {gpu_count}")
                            return f"GPU (数量: {gpu_count})"
                except:
                    pass
            
            # 默认CPU
            self.device_info = "CPU"
            print("🖥️ 使用CPU设备")
            return "CPU"
            
        except Exception as e:
            print(f"设备检测异常: {e}")
            self.device_info = "CPU (检测失败)"
            return "CPU (检测失败)"
    
    def _setup_environment(self):
        """设置环境变量"""
        try:
            # 通用环境变量
            os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
            
            # NPU特定环境变量
            if 'npu' in self.device_info.lower():
                print("🔧 设置NPU环境变量...")
                os.environ['FLAGS_enable_eager_mode'] = '1'
                os.environ['FLAGS_use_mkldnn'] = '0'
                os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.5'
                os.environ['ASCEND_RT_VISIBLE_DEVICES'] = '0,1,2,3,4,5,6,7'
                
                # 尝试设置NPU设备
                try:
                    self.paddle.set_device('npu:0')
                    print("✅ NPU设备设置成功")
                except:
                    print("⚠️ NPU设备设置失败，使用默认设备")
            
            print("✅ 环境变量设置完成")
            
        except Exception as e:
            print(f"环境变量设置异常: {e}")
    
    def is_ready(self):
        """检查OCR是否就绪"""
        return self.ready and self.ocr is not None
    
    def get_device_info(self):
        """获取设备信息"""
        return self.device_info
    
    def process(self, file_path):
        """
        处理文件进行OCR识别
        
        Args:
            file_path: 文件路径
            
        Returns:
            OCR识别结果
        """
        if not self.is_ready():
            return {"error": "OCR服务未就绪"}
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {"error": f"文件不存在: {file_path}"}
            
            # 检查文件类型
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.pdf', '.png', '.jpg', '.jpeg']:
                return {"error": f"不支持的文件类型: {file_ext}"}
            
            print(f"🔍 开始OCR识别: {file_path}")
            start_time = time.time()
            
            # 执行OCR识别
            result = self.ocr.ocr(file_path)
            
            end_time = time.time()
            print(f"⏱️ OCR识别耗时: {end_time - start_time:.2f}秒")
            
            # 处理结果
            if not result:
                print("⚠️ OCR结果为空")
                return []
            
            # 提取文本内容
            text_results = self._extract_texts(result)
            print(f"📝 提取了 {len(text_results)} 页的文本内容")
            
            return text_results
            
        except Exception as e:
            error_msg = f"OCR处理异常: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"异常详情: {traceback.format_exc()}")
            return {"error": error_msg}
    
    def _extract_texts(self, ocr_result):
        """
        从OCR结果中提取文本
        
        Args:
            ocr_result: PaddleOCR的原始结果
            
        Returns:
            提取的文本列表
        """
        try:
            text_results = []
            
            for page_result in ocr_result:
                if page_result:
                    page_texts = []
                    for line in page_result:
                        if len(line) >= 2 and len(line[1]) >= 1:
                            text = line[1][0]  # 提取识别的文本
                            confidence = line[1][1] if len(line[1]) >= 2 else 0.0
                            
                            # 只保留置信度较高的文本
                            if confidence > 0.5:
                                page_texts.append(text)
                    
                    text_results.append(page_texts)
                else:
                    text_results.append([])
            
            return text_results
            
        except Exception as e:
            print(f"文本提取异常: {e}")
            return []
    
    def get_status(self):
        """获取OCR处理器状态"""
        return {
            'ready': self.ready,
            'device': self.device_info,
            'ocr_initialized': self.ocr is not None
        }
