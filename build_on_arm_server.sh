#!/bin/bash

# ARM服务器上的PaddleOCR昇腾NPU版本Docker构建脚本

set -e

echo "========================================="
echo "PaddleOCR 昇腾NPU版本 Docker构建脚本"
echo "适用于ARM64服务器直接构建"
echo "========================================="
echo

# 检查是否为root用户或在docker组中
if [ "$EUID" -ne 0 ] && ! groups | grep -q docker; then
    echo "❌ 错误: 当前用户无Docker权限"
    echo "解决方案:"
    echo "1. 使用sudo运行此脚本"
    echo "2. 或将用户添加到docker组: sudo usermod -aG docker $USER"
    exit 1
fi

# 检查系统架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 当前系统不是ARM64架构"
    echo "检测到架构: $ARCH"
    echo "此脚本仅适用于ARM64(aarch64)架构"
    exit 1
fi

echo "✓ ARM64架构确认"
echo

# 检查Docker是否运行
echo "检查Docker状态..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行"
    echo "尝试启动Docker服务..."
    
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start docker
        sleep 3
        if docker info >/dev/null 2>&1; then
            echo "✓ Docker服务启动成功"
        else
            echo "❌ Docker服务启动失败，请手动检查"
            exit 1
        fi
    else
        echo "请手动启动Docker服务"
        exit 1
    fi
else
    echo "✓ Docker运行正常"
fi
echo

# 检查昇腾设备
echo "检查昇腾NPU设备..."
if ls /dev/davinci* >/dev/null 2>&1; then
    echo "✓ 检测到昇腾设备:"
    ls -la /dev/davinci* | head -5
    
    # 检查昇腾软件栈
    if [ -d "/usr/local/Ascend" ]; then
        echo "✓ 昇腾软件栈已安装"
        
        # 显示NPU信息
        if [ -f "/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi" ]; then
            echo "NPU设备信息:"
            /usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info 2>/dev/null | head -10 || echo "无法获取详细NPU信息"
        fi
    else
        echo "⚠️  警告: 昇腾软件栈未安装在 /usr/local/Ascend"
        echo "   容器将依赖宿主机的昇腾环境"
    fi
else
    echo "⚠️  警告: 未检测到昇腾设备，将使用CPU模式"
fi
echo

# 检查必要文件
echo "检查项目文件..."
REQUIRED_FILES=("Dockerfile.ascend" "requirements.ascend.txt" "app.py" "ocr_processor.py" "run.py" ".env")
missing_files=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 错误: 缺少必要文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo "✓ 所有必要文件检查完成"
echo

# 检查CANN工具包
CANN_FILE="Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run"
if [ -f "$CANN_FILE" ]; then
    echo "✓ 找到昇腾CANN工具包: $CANN_FILE"
    echo "   文件大小: $(du -h $CANN_FILE | cut -f1)"
else
    echo "⚠️  警告: 未找到昇腾CANN工具包文件: $CANN_FILE"
    echo "   Docker镜像将不包含CANN工具包"
    echo "   请确保目标服务器已安装CANN工具包"
    echo
    read -p "是否继续构建? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "构建已取消"
        exit 1
    fi
fi
echo

# 检查磁盘空间
echo "检查磁盘空间..."
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
REQUIRED_SPACE=5242880  # 5GB in KB

if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
    echo "⚠️  警告: 磁盘空间可能不足"
    echo "   可用空间: $(df -h . | tail -1 | awk '{print $4}')"
    echo "   建议空间: 5GB以上"
    echo
    read -p "是否继续构建? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "构建已取消"
        exit 1
    fi
else
    echo "✓ 磁盘空间充足: $(df -h . | tail -1 | awk '{print $4}')"
fi
echo

# 清理旧的构建缓存
echo "清理Docker构建缓存..."
docker builder prune -f >/dev/null 2>&1 || true
echo "✓ 构建缓存已清理"
echo

# 设置镜像信息
IMAGE_NAME="paddleocr-arm64-server"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "开始构建Docker镜像..."
echo "镜像名称: $FULL_IMAGE_NAME"
echo "构建上下文: $(pwd)"
echo "Dockerfile: Dockerfile.ascend"
echo

# 记录开始时间
START_TIME=$(date)
echo "构建开始时间: $START_TIME"
echo

# 显示构建命令
echo "执行构建命令:"
echo "docker build -f Dockerfile.ascend -t $FULL_IMAGE_NAME --no-cache --progress=plain ."
echo
echo "开始构建..."
echo "========================================"

# 构建Docker镜像（ARM服务器上不需要--platform参数）
docker build -f Dockerfile.ascend -t "$FULL_IMAGE_NAME" --no-cache --progress=plain .

BUILD_RESULT=$?
END_TIME=$(date)

echo "========================================"
echo "构建结束时间: $END_TIME"
echo

if [ $BUILD_RESULT -eq 0 ]; then
    echo "✅ Docker镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo
    
    # 显示镜像信息
    echo "镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    # 验证镜像
    echo "验证镜像..."
    if docker run --rm "$FULL_IMAGE_NAME" python -c "import paddle; print('✅ Paddle version:', paddle.__version__)" 2>/dev/null; then
        echo "✅ 镜像验证成功 - Paddle模块可以正常导入"
    else
        echo "⚠️  镜像验证失败 - Paddle模块导入有问题，但镜像构建成功"
    fi
    echo
    
    echo "后续步骤:"
    echo "1. 运行容器 (昇腾NPU模式):"
    echo "   docker run --name paddleocr-ascend -p 9527:9527 \\"
    echo "     --device=/dev/davinci0:/dev/davinci0 \\"
    echo "     --device=/dev/davinci_manager:/dev/davinci_manager \\"
    echo "     --device=/dev/devmm_svm:/dev/devmm_svm \\"
    echo "     --device=/dev/hisi_hdc:/dev/hisi_hdc \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend:ro \\"
    echo "     -d $FULL_IMAGE_NAME"
    echo
    echo "2. 使用Docker Compose运行:"
    echo "   docker-compose -f docker-compose.ascend.yml up -d"
    echo
    echo "3. 测试服务:"
    echo "   curl http://localhost:9527/health"
    echo
    
else
    echo "❌ Docker镜像构建失败!"
    echo "错误代码: $BUILD_RESULT"
    echo
    echo "常见问题排查:"
    echo "1. 检查网络连接是否正常"
    echo "2. 检查磁盘空间是否充足"
    echo "3. 检查Docker服务状态"
    echo "4. 查看上方的详细错误信息"
    echo
    echo "如需帮助，请提供完整的构建日志"
    exit 1
fi
