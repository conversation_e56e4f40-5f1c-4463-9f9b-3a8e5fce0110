#!/bin/bash

# 模型诊断脚本
# 详细检查模型文件状态

echo "🔍 PaddleOCR 模型诊断"
echo "===================="

if ! docker ps | grep -q paddleocr-service; then
    echo "❌ paddleocr-service 容器未运行"
    exit 1
fi

echo "📋 检查容器内模型状态..."

# 检查 .paddlex 目录结构
echo ""
echo "1️⃣ 检查 .paddlex 目录结构:"
docker exec paddleocr-service find /root/.paddlex -type d 2>/dev/null | head -20

# 检查 official_models 目录
echo ""
echo "2️⃣ 检查 official_models 目录:"
docker exec paddleocr-service ls -la /root/.paddlex/official_models/ 2>/dev/null || echo "❌ official_models 目录不存在"

# 检查每个模型目录
echo ""
echo "3️⃣ 检查各个模型目录:"
models=("PP-LCNet_x1_0_doc_ori" "PP-LCNet_x1_0_textline_ori" "PP-OCRv5_server_det" "PP-OCRv5_server_rec" "UVDoc")

for model in "${models[@]}"; do
    echo "   检查 $model:"
    if docker exec paddleocr-service test -d "/root/.paddlex/official_models/$model"; then
        echo "   ✅ 目录存在"
        # 检查模型文件
        files=$(docker exec paddleocr-service ls /root/.paddlex/official_models/$model/ 2>/dev/null | wc -l)
        echo "   📁 包含 $files 个文件"
        
        # 检查关键模型文件
        if docker exec paddleocr-service ls /root/.paddlex/official_models/$model/*.pdmodel >/dev/null 2>&1; then
            echo "   ✅ 包含 .pdmodel 文件"
        else
            echo "   ❌ 缺少 .pdmodel 文件"
        fi
        
        if docker exec paddleocr-service ls /root/.paddlex/official_models/$model/*.pdiparams >/dev/null 2>&1; then
            echo "   ✅ 包含 .pdiparams 文件"
        else
            echo "   ❌ 缺少 .pdiparams 文件"
        fi
    else
        echo "   ❌ 目录不存在"
    fi
    echo ""
done

# 检查模型文件权限
echo "4️⃣ 检查模型文件权限:"
docker exec paddleocr-service ls -la /root/.paddlex/official_models/*/  2>/dev/null | head -10

# 检查原始压缩文件内容
echo ""
echo "5️⃣ 检查原始压缩文件内容:"
if [ -f "paddlex_models.tar.gz" ]; then
    echo "压缩文件大小: $(ls -lh paddlex_models.tar.gz | awk '{print $5}')"
    echo "压缩文件内容结构:"
    tar -tzf paddlex_models.tar.gz | head -30
else
    echo "❌ paddlex_models.tar.gz 文件不存在"
fi

# 检查 PaddleOCR 版本
echo ""
echo "6️⃣ 检查 PaddleOCR 版本:"
docker exec paddleocr-service python -c "
try:
    import paddleocr
    print('PaddleOCR 版本: 3.1.0 (预期)')
    
    import paddle
    print(f'PaddlePaddle 版本: {paddle.__version__}')
    
    import numpy
    print(f'NumPy 版本: {numpy.__version__}')
except Exception as e:
    print(f'版本检查失败: {e}')
"

# 尝试手动初始化 PaddleOCR
echo ""
echo "7️⃣ 尝试手动初始化 PaddleOCR:"
docker exec paddleocr-service python -c "
import os
print(f'当前工作目录: {os.getcwd()}')
print(f'HOME 目录: {os.path.expanduser(\"~\")}')

# 检查 .paddlex 目录
paddlex_dir = os.path.expanduser('~/.paddlex')
print(f'.paddlex 目录存在: {os.path.exists(paddlex_dir)}')

if os.path.exists(paddlex_dir):
    official_models = os.path.join(paddlex_dir, 'official_models')
    print(f'official_models 目录存在: {os.path.exists(official_models)}')
    
    if os.path.exists(official_models):
        models = os.listdir(official_models)
        print(f'可用模型: {models}')

try:
    from paddleocr import PaddleOCR
    print('尝试初始化 PaddleOCR...')
    # 不要实际初始化，只是导入测试
    print('PaddleOCR 导入成功')
except Exception as e:
    print(f'PaddleOCR 初始化失败: {e}')
"

echo ""
echo "🎯 诊断建议:"
echo "============"

# 基于检查结果给出建议
if docker exec paddleocr-service test -d "/root/.paddlex/official_models"; then
    model_count=$(docker exec paddleocr-service ls /root/.paddlex/official_models/ 2>/dev/null | wc -l)
    if [ "$model_count" -eq 5 ]; then
        echo "✅ 模型目录结构正确"
        echo "🔧 建议尝试:"
        echo "   1. 降级到 PaddleOCR 2.7.x 版本"
        echo "   2. 或使用 CPU 单线程模式"
        echo "   3. 或增加内存限制"
    else
        echo "❌ 模型目录不完整"
        echo "🔧 建议:"
        echo "   1. 重新检查 paddlex_models.tar.gz 文件"
        echo "   2. 确认压缩文件包含所有必要的模型"
    fi
else
    echo "❌ 模型目录结构错误"
    echo "🔧 建议:"
    echo "   1. 重新构建镜像"
    echo "   2. 检查 Dockerfile 中的模型复制逻辑"
fi

echo ""
echo "📞 如需进一步调试:"
echo "   docker exec -it paddleocr-service bash"
echo "   然后手动检查模型文件"
