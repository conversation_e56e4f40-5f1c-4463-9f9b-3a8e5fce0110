#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
在线版本测试脚本
测试PaddleOCR在线版本的功能
"""

import os
import sys
import time
import json
import requests
from PIL import Image, ImageDraw, ImageFont

def create_test_image():
    """创建测试图片"""
    print("📝 创建测试图片...")
    
    # 创建一个简单的测试图片
    width, height = 400, 200
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # 添加测试文本
    test_texts = [
        "PaddleOCR在线测试",
        "Online Version Test",
        "模型自动下载",
        "2024年测试"
    ]
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 18)
    except:
        try:
            # 备用字体
            font = ImageFont.truetype("arial.ttf", 18)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    y_offset = 30
    for text in test_texts:
        draw.text((20, y_offset), text, fill='black', font=font)
        y_offset += 35
    
    # 保存测试图片
    test_image_path = "test_online_ocr.png"
    image.save(test_image_path)
    print(f"✅ 测试图片已创建: {test_image_path}")
    
    return test_image_path

def wait_for_service(max_wait=300):
    """等待服务启动"""
    print(f"⏳ 等待服务启动（最多{max_wait}秒）...")
    print("注意: 首次启动时需要下载模型，可能需要较长时间")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:9527/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务已启动（等待了{i}秒）")
                return True
        except:
            pass
        
        if i % 30 == 0 and i > 0:
            print(f"   仍在等待... {i}/{max_wait}秒")
            print("   可能正在下载模型，请耐心等待")
        time.sleep(1)
    
    print(f"❌ 服务启动超时（{max_wait}秒）")
    return False

def test_health():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    try:
        response = requests.get("http://localhost:9527/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_service_info():
    """测试服务信息"""
    print("ℹ️ 获取服务信息...")
    try:
        response = requests.get("http://localhost:9527/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("✅ 服务信息:")
            print(f"   服务: {info.get('service', 'N/A')}")
            print(f"   版本: {info.get('version', 'N/A')}")
            print(f"   支持格式: {info.get('supported_formats', 'N/A')}")
            return True
        else:
            print(f"❌ 服务信息获取失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务信息获取异常: {e}")
        return False

def test_ocr_service(image_path):
    """测试OCR识别服务"""
    print("🔍 测试OCR识别服务...")
    print("注意: 首次OCR识别会触发模型下载，可能需要几分钟")
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False
    
    try:
        print("📤 发送OCR请求...")
        start_time = time.time()
        
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post("http://localhost:9527/ocr", files=files, timeout=300)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ OCR识别成功（耗时: {duration:.2f}秒）")
            
            if 'texts' in result:
                texts = result['texts']
                print(f"   识别到 {len(texts)} 页内容:")
                for i, page_texts in enumerate(texts, 1):
                    print(f"   第{i}页: {page_texts}")
            elif 'error' in result:
                print(f"   OCR处理错误: {result['error']}")
                return False
            else:
                print(f"   响应: {result}")
            
            return True
        else:
            print(f"❌ OCR识别失败: HTTP {response.status_code}")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ OCR请求超时（可能是模型下载需要时间）")
        print("💡 建议: 等待几分钟后重试")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ OCR请求失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ OCR响应解析失败: {e}")
        return False

def check_container_logs():
    """检查容器日志"""
    print("📋 检查容器日志...")
    try:
        import subprocess
        result = subprocess.run(['docker', 'logs', 'paddleocr-service', '--tail', '20'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("最近的容器日志:")
            print(result.stdout)
        else:
            print("无法获取容器日志")
    except:
        print("无法执行docker命令")

def main():
    """主函数"""
    print("🌐 PaddleOCR 在线版本测试")
    print("=" * 40)
    
    # 等待服务启动
    if not wait_for_service():
        print("❌ 服务未启动，请检查部署状态")
        check_container_logs()
        return False
    
    print()
    
    # 测试健康检查
    if not test_health():
        return False
    
    print()
    
    # 测试服务信息
    if not test_service_info():
        return False
    
    print()
    
    # 创建测试图片
    test_image_path = create_test_image()
    
    print()
    
    # 测试OCR功能
    ocr_success = test_ocr_service(test_image_path)
    
    # 清理测试文件
    try:
        os.remove(test_image_path)
        print(f"🧹 已清理测试图片: {test_image_path}")
    except:
        pass
    
    print()
    print("=" * 40)
    
    if ocr_success:
        print("🎉 在线版本测试完全通过！")
        print("✅ PaddleOCR 在线版本工作正常")
        print("✅ 模型下载和识别功能正常")
        print()
        print("💡 后续步骤:")
        print("   1. 可以正常使用OCR服务")
        print("   2. 如需离线版本，可以稍后配置")
        print("   3. 模型已下载，后续识别会更快")
        return True
    else:
        print("❌ 在线版本测试失败")
        print("🔧 故障排除建议:")
        print("   1. 检查网络连接是否正常")
        print("   2. 查看容器日志: docker logs paddleocr-service")
        print("   3. 检查容器资源: docker stats paddleocr-service")
        print("   4. 重启服务: docker restart paddleocr-service")
        check_container_logs()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
