# 调试用的简化Dockerfile
FROM ubuntu:22.04

WORKDIR /app

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 更新软件源为阿里云镜像
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list

# 安装基本系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3 python3-pip python3-dev \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    curl \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建Python软链接
RUN ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# 复制必要文件
COPY app.py .
COPY ocr_processor.py .
COPY run.py .
COPY .env .

# 配置pip源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip config set install.trusted-host www.paddlepaddle.org.cn

# 升级pip
RUN pip install --upgrade pip setuptools wheel

# 测试基本Python环境
RUN python --version && pip --version

# 安装基础依赖
RUN pip install --no-cache-dir flask==3.0.0 werkzeug==3.0.1

# 测试Flask安装
RUN python -c "import flask; print('Flask version:', flask.__version__)"

# 安装numpy和Pillow
RUN pip install --no-cache-dir "numpy>=1.26.0" "Pillow>=10.1.0" python-dotenv==1.0.0

# 测试numpy安装
RUN python -c "import numpy; print('NumPy version:', numpy.__version__)"

# 尝试安装paddle-custom-npu（这是最关键的步骤）
RUN echo "开始安装paddle-custom-npu..." && \
    pip install --no-cache-dir paddle-custom-npu==3.0.0 \
    -i https://www.paddlepaddle.org.cn/packages/stable/npu/ \
    --trusted-host www.paddlepaddle.org.cn \
    --timeout 600 && \
    echo "paddle-custom-npu安装完成"

# 验证paddle安装
RUN python -c "import paddle; print('✅ Paddle version:', paddle.__version__); print('✅ Paddle imported successfully')"

# 安装PaddleOCR
RUN echo "开始安装paddleocr..." && \
    pip install --no-cache-dir paddleocr==3.0.0 \
    --timeout 600 && \
    echo "paddleocr安装完成"

# 验证paddleocr安装
RUN python -c "import paddleocr; print('✅ PaddleOCR imported successfully')"

# 显示所有已安装的包
RUN pip list

EXPOSE 9527

CMD ["python", "run.py"]
