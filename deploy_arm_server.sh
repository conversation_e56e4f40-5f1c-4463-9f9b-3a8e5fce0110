#!/bin/bash

# ARM服务器部署脚本
# 用于在ARM昇腾服务器上部署PaddleOCR服务

set -e

echo "========================================="
echo "ARM昇腾服务器部署脚本"
echo "========================================="
echo

# 检查是否为ARM64架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 此脚本仅适用于ARM64架构的服务器"
    exit 1
fi

# 检查Docker是否安装和运行
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    echo "   sudo systemctl start docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查昇腾环境
echo "🔍 检查昇腾NPU环境..."
NPU_ENV_FOUND=false

# 检查昇腾驱动目录（按官方推荐方式）
ASCEND_PATHS=("/usr/local/Ascend" "/usr/local/Ascend/driver" "/usr/local/bin/npu-smi" "/usr/local/dcmi")
for path in "${ASCEND_PATHS[@]}"; do
    if [ -e "$path" ]; then
        echo "✅ 找到昇腾组件: $path"
        NPU_ENV_FOUND=true
    else
        echo "⚠️  未找到组件: $path"
    fi
done

# 检查关键库文件
ASCEND_LIBS=("/usr/local/Ascend/driver/lib64/libmsprofiler.so" "/usr/local/Ascend/ascend-toolkit/latest/lib64/libmsprofiler.so")
for lib in "${ASCEND_LIBS[@]}"; do
    if [ -f "$lib" ]; then
        echo "✅ 找到关键库: $lib"
        NPU_ENV_FOUND=true
        break
    fi
done

# 检查npu-smi命令
if command -v npu-smi &> /dev/null; then
    echo "✅ npu-smi命令可用"
    NPU_ENV_FOUND=true
else
    echo "⚠️  npu-smi命令不可用"
fi

if [ "$NPU_ENV_FOUND" = false ]; then
    echo "⚠️  警告: 未检测到完整的昇腾NPU环境，服务将以CPU模式运行"
    read -p "是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 1
    fi
fi

# 设置镜像信息
IMAGE_NAME="paddleocr-official-ascend:latest"
CONTAINER_NAME="paddleocr-official-ascend"

# 检查镜像是否存在
if ! docker images | grep -q "paddleocr-official-ascend"; then
    echo "❌ 错误: 未找到镜像 $IMAGE_NAME"
    echo "请先构建镜像或从其他地方导入镜像"
    echo
    echo "构建镜像命令:"
    echo "  ./build_official_ascend.sh"
    echo
    echo "或导入镜像命令:"
    echo "  docker load -i paddleocr-official-ascend.tar"
    exit 1
fi

echo "✅ 找到镜像: $IMAGE_NAME"

# 停止并删除现有容器（如果存在）
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🔄 停止并删除现有容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 构建Docker运行命令（基于官方推荐方式）
DOCKER_CMD="docker run -d"
DOCKER_CMD="$DOCKER_CMD --name $CONTAINER_NAME"
DOCKER_CMD="$DOCKER_CMD --restart unless-stopped"

# NPU模式和CPU模式使用不同的网络配置
if [ "$NPU_ENV_FOUND" = true ]; then
    # NPU模式：使用host网络，服务直接在宿主机端口运行
    DOCKER_CMD="$DOCKER_CMD --privileged"
    DOCKER_CMD="$DOCKER_CMD --network=host"
    DOCKER_CMD="$DOCKER_CMD --shm-size=128G"
    echo "✅ 使用host网络模式（NPU模式）"
else
    # CPU模式：使用端口映射
    DOCKER_CMD="$DOCKER_CMD -p 9527:9527"
    echo "✅ 使用端口映射模式（CPU模式）"
fi

# 添加昇腾环境挂载（按官方推荐方式）
if [ "$NPU_ENV_FOUND" = true ]; then
    echo "🚀 配置昇腾NPU环境挂载（官方推荐方式）..."

    # 添加工作目录挂载
    DOCKER_CMD="$DOCKER_CMD -v $(pwd):/work -w=/work"

    # 挂载整个Ascend目录（确保包含所有必要的库文件）
    if [ -d "/usr/local/Ascend" ]; then
        DOCKER_CMD="$DOCKER_CMD -v /usr/local/Ascend:/usr/local/Ascend"
        echo "✅ 挂载完整昇腾目录"
    fi

    # 挂载npu-smi工具
    if [ -f "/usr/local/bin/npu-smi" ]; then
        DOCKER_CMD="$DOCKER_CMD -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi"
        echo "✅ 挂载npu-smi工具"
    fi

    # 挂载dcmi目录
    if [ -d "/usr/local/dcmi" ]; then
        DOCKER_CMD="$DOCKER_CMD -v /usr/local/dcmi:/usr/local/dcmi"
        echo "✅ 挂载dcmi目录"
    fi

    # 设置昇腾可见设备环境变量
    DOCKER_CMD="$DOCKER_CMD -e ASCEND_RT_VISIBLE_DEVICES=0,1,2,3,4,5,6,7"
    echo "✅ 设置昇腾可见设备"
fi

# 添加其他环境变量
DOCKER_CMD="$DOCKER_CMD -e TZ=Asia/Shanghai"

# 添加镜像名称
DOCKER_CMD="$DOCKER_CMD $IMAGE_NAME"

echo "🚀 启动容器..."
echo "执行命令: $DOCKER_CMD"
echo

# 执行Docker运行命令
eval $DOCKER_CMD

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    echo
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✅ 容器运行正常"
        
        # 测试健康检查
        echo "🔍 测试服务健康状态..."
        for i in {1..5}; do
            if curl -s http://localhost:9527/health >/dev/null 2>&1; then
                echo "✅ 服务健康检查通过"
                break
            else
                echo "⏳ 等待服务启动... ($i/5)"
                sleep 5
            fi
        done
        
        # 显示服务信息
        echo
        echo "🎉 部署完成!"
        echo "服务地址: http://localhost:9527"
        echo "健康检查: http://localhost:9527/health"
        echo "OCR接口: http://localhost:9527/ocr (POST)"
        echo
        echo "📊 容器状态:"
        docker ps | grep "$CONTAINER_NAME"
        echo
        echo "📝 常用命令:"
        echo "  查看日志: docker logs $CONTAINER_NAME"
        echo "  查看实时日志: docker logs -f $CONTAINER_NAME"
        echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
        echo "  停止服务: docker stop $CONTAINER_NAME"
        echo "  重启服务: docker restart $CONTAINER_NAME"
        echo
        echo "🧪 测试OCR功能:"
        echo "  curl -X POST -F 'file=@test_image.png' http://localhost:9527/ocr"
        
    else
        echo "❌ 容器启动失败"
        echo "查看容器日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
else
    echo "❌ 容器启动失败"
    exit 1
fi
