#!/bin/bash

# 段错误修复脚本
# 诊断和修复PaddleOCR段错误问题

echo "🔧 PaddleOCR段错误诊断和修复"
echo "============================="

# 检查当前容器状态
echo "🔍 检查容器状态..."
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器正在运行"
    
    # 检查容器资源使用
    echo "📊 容器资源使用情况:"
    docker stats paddleocr-service --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
    
    # 检查容器日志
    echo ""
    echo "📋 最近的容器日志:"
    docker logs paddleocr-service --tail 20
    
else
    echo "❌ 容器未运行"
fi

echo ""
echo "🔧 开始修复流程..."

# 停止容器
echo "🛑 停止当前容器..."
docker stop paddleocr-service 2>/dev/null || true
docker rm paddleocr-service 2>/dev/null || true

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 重新构建镜像（使用优化配置）
echo "🔨 重新构建镜像（使用段错误修复配置）..."
if docker build -f Dockerfile.segfault-fix -t paddleocr-service:arm64-latest .; then
    echo "✅ 镜像构建成功"
else
    echo "❌ 镜像构建失败，尝试使用标准配置..."
    if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
        echo "✅ 使用标准配置构建成功"
    else
        echo "❌ 构建失败"
        exit 1
    fi
fi

# 使用优化参数重新部署
echo "🚀 使用优化参数重新部署..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 6g \
    --memory-swap 8g \
    --cpus 2 \
    --shm-size 1g \
    --ulimit core=-1 \
    -e OMP_NUM_THREADS=2 \
    -e MKL_NUM_THREADS=2 \
    -e OPENBLAS_NUM_THREADS=2 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    -e FLAGS_allocator_strategy=auto_growth \
    -e FLAGS_fraction_of_gpu_memory_to_use=0.3 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

echo ""
echo "🎉 段错误修复完成！"
echo "=================="
echo "✅ 服务已重新部署并优化"
echo "📋 优化措施:"
echo "   - 增加内存限制到6GB"
echo "   - 设置共享内存为1GB"
echo "   - 限制线程数量避免冲突"
echo "   - 启用内存增长策略"
echo "   - 禁用自定义设备避免冲突"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   curl -X POST -F \"file=@test_image.png\" http://localhost:9527/ocr"
echo ""

echo "✅ 修复流程完成！"
