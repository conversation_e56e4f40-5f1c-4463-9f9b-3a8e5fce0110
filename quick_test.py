#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本 - 验证PaddleOCR服务是否正常工作
"""

import requests
import json
import time
import sys

def test_health():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    try:
        response = requests.get("http://localhost:9527/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_service_info():
    """测试服务信息"""
    print("ℹ️ 获取服务信息...")
    try:
        response = requests.get("http://localhost:9527/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("✅ 服务信息:")
            print(f"   服务: {info.get('service', 'N/A')}")
            print(f"   版本: {info.get('version', 'N/A')}")
            return True
        else:
            print(f"❌ 服务信息获取失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务信息获取异常: {e}")
        return False

def wait_for_service(max_wait=60):
    """等待服务启动"""
    print(f"⏳ 等待服务启动（最多{max_wait}秒）...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:9527/health", timeout=3)
            if response.status_code == 200:
                print(f"✅ 服务已启动（等待了{i}秒）")
                return True
        except:
            pass
        
        if i % 10 == 0 and i > 0:
            print(f"   等待中... {i}/{max_wait}秒")
        time.sleep(1)
    
    print(f"❌ 服务启动超时（{max_wait}秒）")
    return False

def main():
    """主函数"""
    print("🧪 PaddleOCR服务快速测试")
    print("=" * 30)
    
    # 等待服务启动
    if not wait_for_service():
        print("❌ 服务未启动，请检查容器状态")
        print("检查命令: docker logs paddleocr-service")
        return False
    
    print()
    
    # 测试健康检查
    if not test_health():
        return False
    
    print()
    
    # 测试服务信息
    if not test_service_info():
        return False
    
    print()
    print("🎉 快速测试通过！")
    print("=" * 30)
    print("✅ PaddleOCR服务正常运行")
    print("🔗 服务地址: http://localhost:9527")
    print("📋 API文档: http://localhost:9527/")
    print()
    print("🧪 完整测试命令:")
    print("   python3 test_deployment.py")
    print()
    print("🔍 手动OCR测试:")
    print("   curl -X POST -F \"file=@test_image.png\" http://localhost:9527/ocr")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
