#!/bin/bash

# 模型路径修复脚本
# 修复模型文件路径结构问题，解决段错误

echo "🔧 PaddleOCR模型路径修复脚本"
echo "============================"

# 检查压缩文件
if [ ! -f "paddlex_models.tar.gz" ]; then
    echo "❌ paddlex_models.tar.gz 文件不存在"
    exit 1
fi

echo "✅ 发现模型文件: paddlex_models.tar.gz"

# 显示压缩文件结构
echo "📋 压缩文件结构:"
tar -tzf paddlex_models.tar.gz | head -20

# 检查当前容器状态
if docker ps | grep -q paddleocr-service; then
    echo ""
    echo "🔍 检查当前容器中的模型结构:"
    docker exec paddleocr-service ls -la /root/.paddleocr/ 2>/dev/null || echo "无法访问容器"
    
    echo ""
    echo "🛑 停止当前容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 重新构建镜像（使用修复后的模型路径逻辑）
echo ""
echo "🔨 重新构建镜像（修复模型路径）..."
echo "这将正确处理 .paddlex/official_models/ 结构"

start_time=$(date +%s)

if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 验证模型结构
echo ""
echo "🔍 验证模型结构..."
docker run --rm paddleocr-service:arm64-latest ls -la /root/.paddleocr/

expected_models=("PP-LCNet_x1_0_doc_ori" "PP-LCNet_x1_0_textline_ori" "PP-OCRv5_server_det" "PP-OCRv5_server_rec" "UVDoc")
echo ""
echo "📦 检查必需的模型目录:"
for model in "${expected_models[@]}"; do
    if docker run --rm paddleocr-service:arm64-latest test -d "/root/.paddleocr/$model"; then
        echo "✅ $model - 存在"
    else
        echo "❌ $model - 缺失"
    fi
done

# 重新部署服务
echo ""
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 6g \
    --cpus 2 \
    --shm-size 1g \
    -e OMP_NUM_THREADS=2 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 检查模型加载日志
echo ""
echo "📋 检查模型加载日志:"
docker logs paddleocr-service 2>&1 | grep -E "(模型|model|预加载|可用模型)" || echo "未找到相关日志"

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 测试OCR功能
echo ""
echo "🧪 测试OCR功能..."
echo "创建测试图片..."

# 创建一个简单的测试图片
python3 -c "
from PIL import Image, ImageDraw, ImageFont
import os

# 创建测试图片
img = Image.new('RGB', (400, 200), color='white')
draw = ImageDraw.Draw(img)

try:
    font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', 24)
except:
    font = ImageFont.load_default()

draw.text((50, 50), 'PaddleOCR测试', fill='black', font=font)
draw.text((50, 100), 'Model Path Fix Test', fill='black', font=font)

img.save('test_model_fix.png')
print('✅ 测试图片已创建: test_model_fix.png')
" 2>/dev/null || echo "⚠️ 无法创建测试图片，请手动准备"

if [ -f "test_model_fix.png" ]; then
    echo "🔍 测试OCR识别..."
    response=$(curl -s -X POST -F "file=@test_model_fix.png" http://localhost:9527/ocr)
    
    if echo "$response" | grep -q "texts"; then
        echo "✅ OCR功能测试成功"
        echo "识别结果: $response"
    else
        echo "❌ OCR功能测试失败"
        echo "响应: $response"
        echo ""
        echo "容器日志:"
        docker logs paddleocr-service --tail 20
    fi
    
    # 清理测试文件
    rm -f test_model_fix.png
else
    echo "⚠️ 跳过OCR功能测试（无测试图片）"
fi

echo ""
echo "🎉 模型路径修复完成！"
echo "====================="
echo "✅ 模型文件已正确放置到 /root/.paddleocr/ 目录"
echo "✅ 服务正常运行，段错误问题应该已解决"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🧪 完整测试命令:"
echo "   python3 test_deployment.py"
echo ""
echo "🔍 查看模型结构:"
echo "   docker exec paddleocr-service ls -la /root/.paddleocr/"
echo ""

echo "✅ 修复流程完成！"
