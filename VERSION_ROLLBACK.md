# PaddleOCR 版本回退说明

## 🔄 版本回退原因

由于 PaddleOCR 3.1.0 在 ARM64 环境下存在稳定性问题，特别是段错误（Segmentation fault）问题，我们决定回退到经过验证的稳定版本。

## 📊 版本对比

| 组件 | 之前版本 | 稳定版本 | 说明 |
|------|----------|----------|------|
| **PaddleOCR** | 3.1.0 | **2.7.3** | 稳定版本，ARM64兼容性好 |
| **PaddlePaddle** | 3.1.0 | **2.5.2** | 稳定版本，无段错误问题 |
| **Python** | 3.12 | **3.9** | 更好的兼容性 |
| **NumPy** | 1.26.4 | **1.26.4** | 保持不变 |

## 🎯 稳定版本优势

### 1. **经过验证的稳定性**
- ✅ 在ARM64环境下经过大量测试
- ✅ 无段错误问题
- ✅ 内存使用稳定

### 2. **更好的兼容性**
- ✅ 与OpenBlas兼容性好
- ✅ 支持单线程和多线程模式
- ✅ 模型加载稳定

### 3. **功能完整性**
- ✅ 支持中英文OCR识别
- ✅ 支持角度分类
- ✅ 支持PDF文件处理
- ✅ API接口完全兼容

## 🔧 主要变化

### 1. **依赖版本调整**
```txt
# requirements.txt
paddlepaddle==2.5.2  # 从 3.1.0 回退
paddleocr==2.7.3     # 从 3.1.0 回退
```

### 2. **Dockerfile优化**
- 使用 Python 3.9 基础镜像
- 移除了 `enable_mkldnn` 参数（2.7.3不支持）
- 优化了线程配置

### 3. **部署配置调整**
- 强制使用单线程配置避免冲突
- 减少内存限制到4GB
- 优化共享内存设置

## 🚀 部署稳定版本

### 快速部署
```bash
# 给脚本执行权限
chmod +x deploy_stable.sh

# 部署稳定版本
./deploy_stable.sh
```

### 手动部署
```bash
# 构建镜像
docker build -f Dockerfile.online -t paddleocr-service:arm64-latest .

# 部署服务
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    --shm-size 512m \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    paddleocr-service:arm64-latest
```

## 🧪 测试验证

### 基本功能测试
```bash
# 健康检查
curl http://localhost:9527/health

# 服务信息
curl http://localhost:9527/

# OCR测试
python3 test_deployment.py
```

### 容器内测试
```bash
# 进入容器
docker exec -it paddleocr-service bash

# 命令行OCR测试
paddleocr --image_dir /path/to/image.png
```

## 📋 已解决的问题

### 1. **段错误问题**
- ❌ **问题**: PaddleOCR 3.1.0 在ARM64环境下频繁出现段错误
- ✅ **解决**: 回退到2.7.3稳定版本，无段错误问题

### 2. **线程冲突问题**
- ❌ **问题**: OpenBlas多线程冲突导致崩溃
- ✅ **解决**: 使用单线程配置，避免冲突

### 3. **模型加载问题**
- ❌ **问题**: 3.1.0版本模型路径变化导致加载失败
- ✅ **解决**: 2.7.3使用传统路径，加载稳定

### 4. **内存使用问题**
- ❌ **问题**: 3.1.0版本内存使用不稳定
- ✅ **解决**: 2.7.3内存使用稳定，可预测

## 🔮 未来升级计划

### 短期计划（1-3个月）
1. **监控稳定性**: 持续监控2.7.3版本的稳定性
2. **功能验证**: 确保所有功能正常工作
3. **性能优化**: 针对ARM64环境进行性能调优

### 中期计划（3-6个月）
1. **关注官方更新**: 跟踪PaddleOCR官方对ARM64支持的改进
2. **测试新版本**: 在测试环境中验证新版本的稳定性
3. **准备升级**: 当新版本稳定后准备升级方案

### 长期计划（6个月以上）
1. **升级到稳定的新版本**: 当官方解决ARM64问题后升级
2. **利用新特性**: 使用新版本的高级功能
3. **持续优化**: 根据实际使用情况持续优化

## 📞 技术支持

如果在使用稳定版本过程中遇到问题：

### 常见问题排查
1. **服务启动失败**
   ```bash
   docker logs paddleocr-service
   ```

2. **OCR识别失败**
   ```bash
   docker exec paddleocr-service python -c "from paddleocr import PaddleOCR; print('OK')"
   ```

3. **内存不足**
   ```bash
   docker stats paddleocr-service
   ```

### 回滚到更早版本
如果2.7.3仍有问题，可以考虑回滚到2.6.x版本：
```bash
# 修改 requirements.txt
paddlepaddle==2.4.2
paddleocr==2.6.1
```

## 📝 更新日志

- **2025-01-XX**: 发现PaddleOCR 3.1.0在ARM64环境下不稳定
- **2025-01-XX**: 决定回退到2.7.3稳定版本
- **2025-01-XX**: 完成版本回退和测试验证
- **2025-01-XX**: 部署稳定版本，解决段错误问题

---

🎯 **总结**: 通过回退到PaddleOCR 2.7.3 + PaddlePaddle 2.5.2，我们获得了一个在ARM64环境下稳定可靠的OCR服务，避免了3.1.0版本的段错误问题。
