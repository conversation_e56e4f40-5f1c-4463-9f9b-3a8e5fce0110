#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试OCR解析逻辑的脚本
用于验证错误信息过滤是否正确
"""

from ocr_subprocess import OCRSubprocessor

def test_parsing():
    """测试解析逻辑"""

    # 模拟您实际遇到的错误输出
    test_stderr = """I0731 11:59:56.110344  3201 init.cc:238] ENV =/usr/local/lib/python3.10/dist-packages/paddle_custom_device
Traceback (most recent call last):
  File "/usr/local/bin/paddleocr", line 8, in <module>
    sys.exit(console_entry())
  File "/usr/local/lib/python3.10/dist-packages/paddleocr/__main__.py", line 26, in console_entry
    main()
  File "/usr/local/lib/python3.10/dist-packages/paddleocr/_cli.py", line 126, in main
    _execute(args)
  File "/usr/local/lib/python3.10/dist-packages/paddleocr/_cli.py", line 115, in _execute
    args.executor(args)
  File "/usr/local/lib/python3.10/dist-packages/paddleocr/_pipelines/ocr.py", line 630, in execute_with_args
    perform_simple_inference(PaddleOCR, params)
  File "/usr/local/lib/python3.10/dist-packages/paddleocr/_utils/cli.py", line 67, in perform_simple_inference
    for i, res in enumerate(result):
  File "/usr/local/lib/python3.10/dist-packages/paddlex/inference/pipelines/_parallel.py", line 129, in predict
    yield from self._pipeline.predict(
  File "/usr/local/lib/python3.10/dist-packages/paddlex/inference/pipelines/ocr/pipeline.py", line 333, in predict
    image_arrays = self.img_reader(batch_data.instances)
  File "/usr/local/lib/python3.10/dist-packages/paddlex/inference/common/reader/image_reader.py", line 49, in __call__
    return [self.read(img) for img in imgs]
  File "/usr/local/lib/python3.10/dist-packages/paddlex/inference/common/reader/image_reader.py", line 49, in <listcomp>
    return [self.read(img) for img in imgs]
  File "/usr/local/lib/python3.10/dist-packages/paddlex/inference/common/reader/image_reader.py", line 59, in read
    raise Exception(f"Image read Error: {img}")
ValueError: (InvalidArgument) Fail to open library
  [Hint: dso_handle should not be null.] (at /paddle/paddle/fluid/platform/init.cc:153)"""

    processor = OCRSubprocessor()

    print("🧪 测试错误检测...")
    is_error = processor._is_mainly_error_output(test_stderr)
    print(f"是否主要是错误输出: {is_error}")

    print("\n🧪 测试stderr内容提取...")
    useful_content = processor._extract_useful_content_from_stderr(test_stderr)
    print(f"提取的有用内容: '{useful_content}'")

    print("\n🧪 测试完整解析...")
    result = processor._parse_ocr_output(test_stderr)
    print(f"解析结果: {result}")

if __name__ == "__main__":
    test_parsing()
