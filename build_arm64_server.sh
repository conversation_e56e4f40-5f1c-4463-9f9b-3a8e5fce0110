#!/bin/bash

# ARM64服务器上的Docker构建脚本
# 适用于直接在ARM64昇腾NPU服务器上构建镜像

set -e

echo "========================================="
echo "PaddleOCR ARM64服务器构建脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 此脚本只能在ARM64 (aarch64) 系统上运行"
    echo "当前架构: $ARCH"
    exit 1
fi

echo "✓ 系统架构检查通过"
echo

# 检查Docker是否安装和运行
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装"
    echo "请先安装Docker: https://docs.docker.com/engine/install/"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    echo "尝试启动Docker服务: sudo systemctl start docker"
    exit 1
fi

echo "✓ Docker运行状态正常"
echo

# 检查昇腾设备
echo "检查昇腾NPU设备..."
if [ -e "/dev/davinci0" ]; then
    echo "✓ 检测到昇腾NPU设备: /dev/davinci0"
    
    # 检查昇腾驱动
    if [ -d "/usr/local/Ascend" ]; then
        echo "✓ 检测到昇腾软件栈: /usr/local/Ascend"
        
        # 显示CANN版本信息
        if [ -f "/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi" ]; then
            echo "昇腾设备信息:"
            /usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info || true
        fi
    else
        echo "⚠️  警告: 未检测到昇腾软件栈，请确保已安装CANN工具包"
    fi
else
    echo "⚠️  警告: 未检测到昇腾NPU设备，将使用CPU模式"
fi
echo

# 检查必要文件
REQUIRED_FILES=("Dockerfile.ascend" "requirements.ascend.txt" "app.py" "ocr_processor.py" "run.py")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必要文件: $file"
        exit 1
    fi
done

echo "✓ 所有必要文件检查完成"
echo

# 设置镜像标签
IMAGE_NAME="paddleocr-arm64-server"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "开始构建Docker镜像: $FULL_IMAGE_NAME"
echo "构建上下文: $(pwd)"
echo

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "清理旧镜像..."
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
    docker system prune -f
    echo "✓ 清理完成"
    echo
fi

# 构建Docker镜像
echo "执行构建命令..."
docker build \
    -f Dockerfile.ascend \
    -t "$FULL_IMAGE_NAME" \
    --progress=plain \
    --no-cache \
    .

if [ $? -eq 0 ]; then
    echo
    echo "✅ Docker镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo
    
    # 显示镜像信息
    echo "镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    # 询问是否立即运行
    read -p "是否立即运行容器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "启动容器..."
        ./run_container.sh
    else
        echo "后续步骤:"
        echo "1. 运行容器 (自动检测昇腾设备):"
        echo "   ./run_container.sh"
        echo
        echo "2. 使用Docker Compose运行:"
        echo "   docker-compose -f docker-compose.ascend.yml up -d"
        echo
        echo "3. 手动运行 (昇腾NPU模式):"
        echo "   docker run --name paddleocr-arm64-server -p 9527:9527 \\"
        echo "     --device=/dev/davinci0:/dev/davinci0 \\"
        echo "     --device=/dev/davinci_manager:/dev/davinci_manager \\"
        echo "     --device=/dev/devmm_svm:/dev/devmm_svm \\"
        echo "     --device=/dev/hisi_hdc:/dev/hisi_hdc \\"
        echo "     -v /usr/local/Ascend:/usr/local/Ascend:ro \\"
        echo "     -d $FULL_IMAGE_NAME"
        echo
        echo "4. 测试服务:"
        echo "   curl http://localhost:9527/health"
    fi
    
else
    echo
    echo "❌ Docker镜像构建失败!"
    echo "请检查构建日志中的错误信息"
    echo
    echo "常见问题排查:"
    echo "1. 检查网络连接是否正常"
    echo "2. 检查磁盘空间是否充足: df -h"
    echo "3. 检查Docker服务状态: sudo systemctl status docker"
    echo "4. 查看详细错误日志: docker build --no-cache -f Dockerfile.ascend -t $FULL_IMAGE_NAME ."
    exit 1
fi
