@echo off
chcp 65001 >nul
echo =========================================
echo 分步构建 PaddleOCR 昇腾NPU Docker镜像
echo =========================================
echo.

REM 检查Docker状态
echo 步骤1: 检查Docker状态...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行或未安装
    echo 请确保:
    echo 1. Docker Desktop已安装
    echo 2. Docker Desktop正在运行
    echo 3. 在Docker Desktop设置中启用实验性功能
    pause
    exit /b 1
)
echo ✓ Docker运行正常
echo.

REM 检查必要文件
echo 步骤2: 检查必要文件...
set "REQUIRED_FILES=Dockerfile.ascend requirements.ascend.txt app.py ocr_processor.py run.py .env"
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        echo ❌ 错误: 缺少必要文件: %%f
        pause
        exit /b 1
    )
    echo ✓ 找到文件: %%f
)
echo.

REM 测试Dockerfile语法
echo 步骤3: 测试Dockerfile语法...
echo 使用简化的测试Dockerfile验证语法...
docker build -f Dockerfile.test -t test-syntax . --no-cache
if %errorlevel% neq 0 (
    echo ❌ Dockerfile语法测试失败
    echo 请检查Dockerfile.test的内容
    pause
    exit /b 1
)
echo ✓ Dockerfile语法正确
echo.

REM 清理测试镜像
docker rmi test-syntax >nul 2>&1

REM 开始构建主镜像
echo 步骤4: 构建ARM64镜像...
echo 这可能需要10-30分钟，请耐心等待...
echo.

set "IMAGE_NAME=paddleocr-ascend"
set "IMAGE_TAG=latest"

echo 构建命令:
echo docker build --platform linux/arm64 -f Dockerfile.ascend -t %IMAGE_NAME%:%IMAGE_TAG% . --progress=plain
echo.

docker build --platform linux/arm64 -f Dockerfile.ascend -t %IMAGE_NAME%:%IMAGE_TAG% . --progress=plain

if %errorlevel% eq 0 (
    echo.
    echo ✅ 镜像构建成功!
    echo.
    echo 镜像信息:
    docker images %IMAGE_NAME%
    echo.
    echo 后续步骤:
    echo 1. 保存镜像: docker save %IMAGE_NAME%:%IMAGE_TAG% -o paddleocr-ascend-arm64.tar
    echo 2. 传输到ARM64服务器
    echo 3. 在服务器上加载: docker load -i paddleocr-ascend-arm64.tar
    echo 4. 运行服务: ./run_container.sh
) else (
    echo.
    echo ❌ 镜像构建失败!
    echo.
    echo 常见问题排查:
    echo 1. 检查网络连接是否正常
    echo 2. 检查磁盘空间是否充足 (至少需要10GB)
    echo 3. 确保Docker Desktop已启用实验性功能
    echo 4. 尝试重启Docker Desktop
    echo.
    echo 如果问题持续，请尝试:
    echo 1. 使用 build_ascend_docker.bat 脚本
    echo 2. 或在ARM64服务器上直接构建
)

echo.
pause
