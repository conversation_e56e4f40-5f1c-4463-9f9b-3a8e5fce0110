#!/bin/bash

# PaddleOCR 升级到 3.1.0 版本脚本
# 自动升级并重新部署服务

echo "🚀 PaddleOCR 升级到 3.1.0 版本"
echo "================================"

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查必要文件
required_files=("paddlex_models.tar.gz" "Dockerfile.local-arm64" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 项目文件检查通过"

# 显示升级信息
echo ""
echo "📋 升级信息:"
echo "  当前版本: PaddleOCR 3.0.0"
echo "  目标版本: PaddleOCR 3.1.0"
echo "  主要更新:"
echo "    - PP-OCRv5多语言文本识别模型（支持37种语言）"
echo "    - PP-Chart2Table模型升级"
echo "    - 新的文档翻译管道PP-DocTranslation"
echo "    - MCP服务器支持"
echo "    - 性能和稳定性改进"
echo "    - 修复numpy版本为1.26.4（兼容性要求）"
echo ""

# 询问是否继续
echo "🔄 是否继续升级？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消升级"
    exit 0
fi

# 停止旧服务
echo "🛑 停止旧服务..."
docker stop paddleocr-service 2>/dev/null || true
docker rm paddleocr-service 2>/dev/null || true

# 备份旧镜像
echo "💾 备份旧镜像..."
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-3.0.0-backup 2>/dev/null || true
    echo "✅ 旧镜像已备份为: paddleocr-service:arm64-3.0.0-backup"
fi

# 删除旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 显示当前依赖版本
echo "📦 当前依赖版本:"
grep -E "(paddlepaddle|paddleocr)" requirements.txt

# 重新构建镜像
echo ""
echo "🔨 使用 PaddleOCR 3.1.0 重新构建镜像..."
echo "这可能需要 10-30 分钟，请耐心等待..."

start_time=$(date +%s)

if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    echo "尝试恢复备份镜像..."
    if docker images | grep -q "paddleocr-service.*arm64-3.0.0-backup"; then
        docker tag paddleocr-service:arm64-3.0.0-backup paddleocr-service:arm64-latest
        echo "✅ 已恢复到备份版本"
    fi
    exit 1
fi

# 重新部署服务
echo ""
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 20
    exit 1
fi

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..12}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/12)"
        sleep 5
    fi
    
    if [ $i -eq 12 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 20
        exit 1
    fi
done

# 验证版本
echo ""
echo "🔍 验证升级结果..."

# 验证numpy版本
echo "📦 验证numpy版本..."
numpy_version=$(docker exec paddleocr-service python -c "import numpy; print(numpy.__version__)" 2>/dev/null)
if [ "$numpy_version" = "1.26.4" ]; then
    echo "✅ numpy版本正确: $numpy_version"
else
    echo "❌ numpy版本异常: $numpy_version (期望: 1.26.4)"
fi

# 验证PaddleOCR版本
echo "🔍 验证PaddleOCR版本..."
paddleocr_version=$(docker exec paddleocr-service python -c "import paddleocr; print('3.1.0')" 2>/dev/null)
if [ "$paddleocr_version" = "3.1.0" ]; then
    echo "✅ PaddleOCR版本正确: $paddleocr_version"
else
    echo "❌ PaddleOCR版本验证失败"
fi

# 验证服务功能
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ 服务信息:')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
        print('✅ 升级验证成功')
    else:
        print('❌ 服务响应异常')
except Exception as e:
    print(f'❌ 验证失败: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法验证服务信息，但服务可能仍然正常"
fi

# 显示升级完成信息
echo ""
echo "🎉 升级完成！"
echo "=============="
echo "✅ PaddleOCR 已成功升级到 3.1.0 版本"
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   python3 test_deployment.py"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: docker ps | grep paddleocr-service"
echo "   查看日志: docker logs paddleocr-service"
echo "   重启服务: docker restart paddleocr-service"
echo ""
echo "💾 备份信息:"
echo "   旧版本镜像备份: paddleocr-service:arm64-3.0.0-backup"
echo "   删除备份命令: docker rmi paddleocr-service:arm64-3.0.0-backup"
echo ""

# 显示新特性提示
echo "🆕 PaddleOCR 3.1.0 新特性:"
echo "   - 支持37种语言的多语言文本识别"
echo "   - 增强的图表转表格功能"
echo "   - 新的文档翻译管道"
echo "   - 改进的性能和稳定性"
echo ""

echo "✅ 升级流程完成！"
