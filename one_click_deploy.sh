#!/bin/bash

# PaddleOCR ARM64一键部署脚本
# 支持环境检查、构建、部署、测试的完整流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示标题
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                PaddleOCR ARM64 一键部署脚本                   ║"
    echo "║                                                              ║"
    echo "║  🚀 自动化构建、部署、测试 PaddleOCR 服务                      ║"
    echo "║  📦 支持模型预加载，无需运行时下载                             ║"
    echo "║  🏗️  针对 ARM64 架构优化                                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查系统架构
check_architecture() {
    log_info "检查系统架构..."
    arch=$(uname -m)
    if [[ "$arch" != "aarch64" && "$arch" != "arm64" ]]; then
        log_error "当前系统架构为 $arch，此脚本仅支持 ARM64 架构"
        exit 1
    fi
    log_success "系统架构检查通过: $arch"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        log_info "正在安装Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        log_warning "Docker已安装，请重新登录后再次运行此脚本"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，正在启动..."
        sudo systemctl start docker
        sudo systemctl enable docker
        sleep 3
        
        if ! docker info >/dev/null 2>&1; then
            log_error "Docker启动失败，请手动检查"
            exit 1
        fi
    fi
    
    log_success "Docker环境检查通过"
    docker --version
}

# 检查系统资源
check_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [ "$total_mem" -lt 4 ]; then
        log_warning "系统内存不足4GB (当前: ${total_mem}GB)，构建可能失败"
        log_info "建议创建swap空间或增加内存"
    else
        log_success "内存检查通过: ${total_mem}GB"
    fi
    
    # 检查磁盘空间
    available_space=$(df . | tail -1 | awk '{print $4}')
    available_gb=$((available_space / 1024 / 1024))
    if [ "$available_gb" -lt 10 ]; then
        log_error "可用磁盘空间不足10GB (当前: ${available_gb}GB)"
        exit 1
    else
        log_success "磁盘空间检查通过: ${available_gb}GB可用"
    fi
}

# 检查项目文件
check_project_files() {
    log_info "检查项目文件..."
    
    required_files=(
        "paddlex_models.tar.gz"
        "Dockerfile.local-arm64"
        "app.py"
        "ocr_processor.py"
        "run.py"
        "requirements.txt"
    )
    
    missing_files=()
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        log_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    log_success "项目文件检查通过"
    
    # 显示模型文件信息
    model_size=$(ls -lh paddlex_models.tar.gz | awk '{print $5}')
    log_info "模型文件大小: $model_size"
}

# 构建镜像
build_image() {
    log_info "开始构建Docker镜像..."
    
    IMAGE_NAME="paddleocr-service:arm64-latest"
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 构建镜像
    if docker build -f Dockerfile.local-arm64 -t "$IMAGE_NAME" .; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        log_success "镜像构建成功，耗时: ${duration}秒"
    else
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "paddleocr-service" | grep "arm64-latest" | awk '{print $3}')
    log_info "镜像大小: $image_size"
}

# 部署服务
deploy_service() {
    log_info "部署PaddleOCR服务..."
    
    CONTAINER_NAME="paddleocr-service"
    PORT="9527"
    
    # 停止并删除旧容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "清理旧容器..."
        docker stop "$CONTAINER_NAME" >/dev/null 2>&1 || true
        docker rm "$CONTAINER_NAME" >/dev/null 2>&1 || true
    fi
    
    # 启动新容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$PORT:$PORT" \
        --restart unless-stopped \
        --memory 4g \
        --cpus 2 \
        --log-opt max-size=100m \
        --log-opt max-file=3 \
        paddleocr-service:arm64-latest
    
    if [ $? -eq 0 ]; then
        log_success "容器启动成功"
    else
        log_error "容器启动失败"
        exit 1
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "容器运行正常"
    else
        log_error "容器未正常运行"
        log_info "容器日志:"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    PORT="9527"
    max_attempts=10
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:$PORT/health" >/dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        else
            log_info "等待服务就绪... ($attempt/$max_attempts)"
            sleep 5
            ((attempt++))
        fi
    done
    
    log_error "健康检查失败"
    log_info "容器日志:"
    docker logs paddleocr-service --tail 20
    return 1
}

# 运行测试
run_tests() {
    log_info "运行部署测试..."
    
    if [ -f "test_deployment.py" ]; then
        if python3 test_deployment.py; then
            log_success "部署测试通过"
        else
            log_warning "部署测试失败，但服务可能仍然可用"
        fi
    else
        log_warning "测试脚本不存在，跳过自动测试"
        log_info "手动测试命令:"
        echo "  curl http://localhost:9527/health"
        echo "  curl -X POST -F \"file=@test_image.png\" http://localhost:9527/ocr"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo ""
    echo "🎉 PaddleOCR服务已成功部署"
    echo "================================"
    echo "📋 服务信息:"
    echo "  容器名称: paddleocr-service"
    echo "  服务端口: 9527"
    echo "  健康检查: http://localhost:9527/health"
    echo "  OCR接口: http://localhost:9527/ocr"
    echo ""
    echo "🔧 管理命令:"
    echo "  查看状态: docker ps | grep paddleocr-service"
    echo "  查看日志: docker logs paddleocr-service"
    echo "  重启服务: docker restart paddleocr-service"
    echo "  停止服务: docker stop paddleocr-service"
    echo ""
    echo "📊 资源使用:"
    docker stats paddleocr-service --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
}

# 主函数
main() {
    show_banner
    
    log_info "开始一键部署流程..."
    echo ""
    
    # 执行检查
    check_architecture
    check_docker
    check_resources
    check_project_files
    echo ""
    
    # 询问是否继续
    echo -e "${YELLOW}🚀 准备开始构建和部署，预计需要10-30分钟，是否继续？(y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    echo ""
    
    # 执行构建和部署
    build_image
    echo ""
    
    deploy_service
    echo ""
    
    health_check
    echo ""
    
    run_tests
    echo ""
    
    show_deployment_info
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，请检查上述日志"; exit 1' ERR

# 执行主函数
main "$@"
