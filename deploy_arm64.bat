@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 PaddleOCR ARM64部署脚本 (Windows版本)
echo ==========================================

REM 配置参数
set IMAGE_FILE=paddleocr-service-arm64.tar
set IMAGE_NAME=paddleocr-service:arm64-latest
set CONTAINER_NAME=paddleocr-service
set PORT=9527

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过

REM 检查镜像文件是否存在
if not exist "%IMAGE_FILE%" (
    echo ❌ 镜像文件 %IMAGE_FILE% 不存在
    echo 请确保已将镜像文件放置在当前目录
    pause
    exit /b 1
)

echo ✅ 发现镜像文件: %IMAGE_FILE%

REM 停止并删除旧容器（如果存在）
docker ps -a | findstr "%CONTAINER_NAME%" >nul
if not errorlevel 1 (
    echo 🧹 清理旧容器...
    docker stop "%CONTAINER_NAME%" >nul 2>&1
    docker rm "%CONTAINER_NAME%" >nul 2>&1
    echo ✅ 旧容器已清理
)

REM 删除旧镜像（如果存在）
docker images | findstr "paddleocr-service" | findstr "arm64" >nul
if not errorlevel 1 (
    echo 🧹 清理旧镜像...
    docker rmi "%IMAGE_NAME%" >nul 2>&1
    echo ✅ 旧镜像已清理
)

REM 加载镜像
echo 📦 加载Docker镜像...
docker load -i "%IMAGE_FILE%"
if errorlevel 1 (
    echo ❌ 镜像加载失败
    pause
    exit /b 1
)

echo ✅ 镜像加载成功

REM 验证镜像
echo 🔍 验证镜像...
docker images | findstr "paddleocr-service" | findstr "arm64" >nul
if errorlevel 1 (
    echo ❌ 镜像验证失败
    pause
    exit /b 1
)

echo ✅ 镜像验证成功
docker images | findstr paddleocr-service

REM 启动容器
echo 🚀 启动PaddleOCR服务容器...
docker run -d --name "%CONTAINER_NAME%" -p "%PORT%:%PORT%" --restart unless-stopped --memory 4g --cpus 2 "%IMAGE_NAME%"

if errorlevel 1 (
    echo ❌ 容器启动失败
    pause
    exit /b 1
)

echo ✅ 容器启动成功

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查容器状态
echo 🔍 检查容器状态...
docker ps | findstr "%CONTAINER_NAME%" >nul
if errorlevel 1 (
    echo ❌ 容器未正常运行
    echo 容器日志:
    docker logs "%CONTAINER_NAME%"
    pause
    exit /b 1
)

echo ✅ 容器运行正常
docker ps | findstr "%CONTAINER_NAME%"

REM 健康检查
echo 🏥 执行健康检查...
set /a attempts=0
:healthcheck
set /a attempts+=1
curl -f "http://localhost:%PORT%/health" >nul 2>&1
if not errorlevel 1 (
    echo ✅ 健康检查通过
    goto healthcheck_success
)

if %attempts% lss 5 (
    echo ⏳ 等待服务就绪... (%attempts%/5)
    timeout /t 5 /nobreak >nul
    goto healthcheck
)

echo ❌ 健康检查失败
echo 容器日志:
docker logs "%CONTAINER_NAME%" --tail 20
pause
exit /b 1

:healthcheck_success

REM 显示服务信息
echo.
echo 🎉 部署成功！
echo ===============
echo 服务名称: %CONTAINER_NAME%
echo 服务端口: %PORT%
echo 健康检查: http://localhost:%PORT%/health
echo OCR接口: http://localhost:%PORT%/ocr
echo.

REM 显示测试命令
echo 📋 测试命令:
echo # 健康检查
echo curl http://localhost:%PORT%/health
echo.
echo # OCR测试（需要准备测试图片）
echo curl -X POST -F "file=@test_image.png" http://localhost:%PORT%/ocr
echo.

REM 显示管理命令
echo 🔧 管理命令:
echo # 查看容器状态
echo docker ps ^| findstr %CONTAINER_NAME%
echo.
echo # 查看容器日志
echo docker logs %CONTAINER_NAME%
echo.
echo # 重启容器
echo docker restart %CONTAINER_NAME%
echo.
echo # 停止容器
echo docker stop %CONTAINER_NAME%
echo.

echo ✅ 部署完成！
pause
