#!/bin/bash

# 快速部署修复后的离线镜像

set -e

echo "========================================="
echo "快速部署修复后的离线PaddleOCR服务"
echo "========================================="
echo

# 设置镜像和容器信息
IMAGE_NAME="paddleocr-offline-ascend:latest"
CONTAINER_NAME="paddleocr-offline"

# 检查镜像是否存在
if ! docker images | grep -q "paddleocr-offline-ascend"; then
    echo "❌ 错误: 未找到镜像 $IMAGE_NAME"
    echo "请先运行: ./rebuild_offline_image.sh"
    exit 1
fi

echo "✅ 找到镜像: $IMAGE_NAME"

# 停止并删除现有容器（如果存在）
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🔄 停止并删除现有容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 检查昇腾环境
echo "🔍 检查昇腾NPU环境..."
NPU_ENV_FOUND=false

ASCEND_PATHS=("/usr/local/Ascend" "/usr/local/bin/npu-smi" "/usr/local/dcmi")
for path in "${ASCEND_PATHS[@]}"; do
    if [ -e "$path" ]; then
        echo "✅ 找到昇腾组件: $path"
        NPU_ENV_FOUND=true
    else
        echo "⚠️  未找到组件: $path"
    fi
done

# 构建启动命令
if [ "$NPU_ENV_FOUND" = true ]; then
    echo "🚀 使用NPU模式启动..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --privileged \
        --network=host \
        --shm-size=128G \
        -v /usr/local/Ascend:/usr/local/Ascend \
        -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
        -v /usr/local/dcmi:/usr/local/dcmi \
        -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
        -e TZ=Asia/Shanghai \
        "$IMAGE_NAME"
else
    echo "🔄 使用CPU模式启动..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        -p 9527:9527 \
        -e TZ=Asia/Shanghai \
        "$IMAGE_NAME"
fi

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    echo
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 15
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✅ 容器运行正常"
        
        # 显示容器信息
        echo
        echo "📊 容器状态:"
        docker ps | grep "$CONTAINER_NAME"
        echo
        
        # 测试健康检查
        echo "🔍 测试服务健康状态..."
        SERVICE_URL="http://localhost:9527"
        
        for i in {1..6}; do
            if curl -s "$SERVICE_URL/health" >/dev/null 2>&1; then
                echo "✅ 服务健康检查通过"
                
                # 获取健康检查详细信息
                echo "📋 健康检查详情:"
                curl -s "$SERVICE_URL/health" | python -m json.tool
                break
            else
                echo "⏳ 等待服务启动... ($i/6)"
                sleep 5
            fi
        done
        
        echo
        echo "🎉 部署完成!"
        echo
        echo "📍 服务信息:"
        echo "  地址: $SERVICE_URL"
        echo "  健康检查: $SERVICE_URL/health"
        echo "  服务信息: $SERVICE_URL/info"
        echo "  OCR接口: $SERVICE_URL/ocr (POST)"
        echo
        echo "🧪 测试OCR功能:"
        echo "  curl -X POST -F 'file=@test_image.jpg' $SERVICE_URL/ocr"
        echo
        echo "📝 管理命令:"
        echo "  查看日志: docker logs $CONTAINER_NAME"
        echo "  实时日志: docker logs -f $CONTAINER_NAME"
        echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
        echo "  重启服务: docker restart $CONTAINER_NAME"
        echo
        echo "✨ 修复内容:"
        echo "  ✅ 使用正确的PaddlePaddle 3.1.0版本"
        echo "  ✅ 使用正确的paddle-custom-npu 3.1.0版本"
        echo "  ✅ 改进的错误检测和处理逻辑"
        echo "  ✅ 更好的OCR结果解析"
        
    else
        echo "❌ 容器启动失败"
        echo "查看容器日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
else
    echo "❌ 容器启动失败"
    exit 1
fi
