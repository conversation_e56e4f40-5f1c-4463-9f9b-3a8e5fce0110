# 直接使用官方PaddleOCR NPU容器
FROM ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84

# 设置工作目录
WORKDIR /work

# 复制服务文件
COPY ocr_service.py .
COPY ocr_utils.py .
COPY start_service.py .

# 安装Flask和其他必要依赖
RUN pip install --no-cache-dir flask==3.0.0 werkzeug==3.0.1

# 安装PaddlePaddle和PaddleOCR（如果容器内没有）
RUN pip install --no-cache-dir paddlepaddle paddle-custom-npu paddleocr==3.1.0

# 设置环境变量
ENV PYTHONPATH=/work:$PYTHONPATH
ENV FLASK_APP=ocr_service.py
ENV FLASK_ENV=production

# 创建临时目录
RUN mkdir -p /tmp

# 暴露端口
EXPOSE 9527

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:9527/health || exit 1

# 启动服务
CMD ["python3", "start_service.py"]
