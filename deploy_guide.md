# PaddleOCR 3.0.0 昇腾NPU完整部署指南

## 概述

本指南提供了从项目打包到服务器部署的完整流程，支持以下部署方式：
- **Windows开发机 → ARM64昇腾NPU服务器**
- **直接在ARM64昇腾NPU服务器上构建和部署**

## 部署架构

```
开发环境 (Windows x64)     →     生产环境 (ARM64 + 昇腾NPU)
├── 项目开发和测试          →     ├── 服务器环境配置
├── Docker镜像构建          →     ├── 镜像加载和运行
└── 镜像打包传输            →     └── 服务监控和维护
```

## 方案一：Windows开发机构建 + ARM64服务器部署

### 第一步：Windows开发机准备

#### 1.1 环境要求
- Windows 10/11 (x64)
- Docker Desktop 4.0+
- 启用Docker Desktop的实验性功能
- 至少16GB内存，50GB可用磁盘空间

#### 1.2 项目准备
```bash
# 克隆或下载项目到本地
cd C:\paddleocr-docker

# 检查必要文件
dir Dockerfile.ascend
dir requirements.ascend.txt
dir build_ascend_docker.bat
```

#### 1.3 构建ARM64镜像
```bash
# 运行构建脚本
build_ascend_docker.bat

# 或手动构建
docker build --platform linux/arm64 -f Dockerfile.ascend -t paddleocr-ascend:latest .
```

#### 1.4 导出镜像
```bash
# 保存镜像为tar文件
docker save paddleocr-ascend:latest -o paddleocr-ascend-arm64.tar

# 检查文件大小
dir paddleocr-ascend-arm64.tar
```

### 第二步：ARM64服务器环境配置

#### 2.1 服务器要求
- ARM64架构 (aarch64)
- Ubuntu 20.04/22.04 或 CentOS 8+
- 昇腾NPU设备和驱动
- 至少8GB内存，100GB可用磁盘空间

#### 2.2 上传文件到服务器
```bash
# 使用scp上传镜像文件
scp paddleocr-ascend-arm64.tar user@server:/opt/

# 上传项目文件
scp -r . user@server:/opt/paddleocr-docker/
```

#### 2.3 服务器环境配置
```bash
# 登录服务器
ssh user@server

# 运行环境配置脚本
cd /opt/paddleocr-docker
sudo chmod +x server_setup.sh
sudo ./server_setup.sh
```

#### 2.4 加载和运行镜像
```bash
# 加载Docker镜像
docker load -i /opt/paddleocr-ascend-arm64.tar

# 运行容器
chmod +x run_container.sh
./run_container.sh
```

## 方案二：直接在ARM64服务器上构建部署

### 第一步：服务器环境配置
```bash
# 上传项目文件到服务器
scp -r . user@server:/opt/paddleocr-docker/

# 登录服务器并配置环境
ssh user@server
cd /opt/paddleocr-docker
sudo chmod +x server_setup.sh
sudo ./server_setup.sh
```

### 第二步：构建和运行
```bash
# 构建Docker镜像
chmod +x build_arm64_server.sh
./build_arm64_server.sh

# 运行容器
./run_container.sh
```

## 昇腾NPU环境配置

### 昇腾驱动安装
```bash
# 1. 下载昇腾驱动和CANN工具包
# 访问: https://www.hiascend.com/software/cann

# 2. 安装驱动
chmod +x Ascend-hdk-*.run
sudo ./Ascend-hdk-*.run --install

# 3. 安装CANN工具包
chmod +x Ascend-cann-toolkit_*.run
sudo ./Ascend-cann-toolkit_*.run --install

# 4. 配置环境变量
echo 'export LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
echo 'export PATH=/usr/local/Ascend/ascend-toolkit/latest/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 验证昇腾设备
```bash
# 检查设备文件
ls -la /dev/davinci*

# 检查设备状态
npu-smi info
```

## 服务部署和管理

### 启动服务
```bash
# 方式1: 使用脚本启动 (推荐)
./run_container.sh

# 方式2: 使用Docker Compose
docker-compose -f docker-compose.ascend.yml up -d

# 方式3: 手动启动
docker run --name paddleocr-ascend -p 9527:9527 \
  --device=/dev/davinci0:/dev/davinci0 \
  --device=/dev/davinci_manager:/dev/davinci_manager \
  --device=/dev/devmm_svm:/dev/devmm_svm \
  --device=/dev/hisi_hdc:/dev/hisi_hdc \
  -v /usr/local/Ascend:/usr/local/Ascend:ro \
  -d paddleocr-ascend:latest
```

### 服务验证
```bash
# 健康检查
curl http://localhost:9527/health

# OCR测试
curl -X POST -F "file=@test_image.png" http://localhost:9527/ocr
```

### 服务监控
```bash
# 查看容器状态
docker ps

# 查看日志
docker logs paddleocr-ascend

# 查看资源使用
docker stats paddleocr-ascend

# 查看昇腾NPU使用情况
npu-smi info
```

## 故障排除

### 常见问题

#### 1. Docker镜像构建失败
```bash
# 检查Docker版本
docker --version

# 清理Docker缓存
docker system prune -a

# 重新构建
docker build --no-cache -f Dockerfile.ascend -t paddleocr-ascend:latest .
```

#### 2. 昇腾NPU设备未检测到
```bash
# 检查设备文件
ls -la /dev/davinci*

# 检查驱动状态
lsmod | grep drv_davinci

# 重启昇腾服务
sudo systemctl restart ascend-hdk
```

#### 3. 容器启动失败
```bash
# 查看详细错误
docker logs paddleocr-ascend

# 检查端口占用
netstat -tlnp | grep 9527

# 检查设备权限
ls -la /dev/davinci*
```

#### 4. OCR识别失败
```bash
# 检查模型下载
docker exec paddleocr-ascend ls -la ~/.paddleocr/

# 检查内存使用
free -h

# 重启容器
docker restart paddleocr-ascend
```

## 性能优化

### 系统优化
```bash
# 调整内核参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 调整ulimit
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf
```

### Docker优化
```bash
# 配置Docker daemon
cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

sudo systemctl restart docker
```

## 安全配置

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 9527/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=9527/tcp
sudo firewall-cmd --reload
```

### SSL/TLS配置 (可选)
```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 配置Nginx反向代理 (推荐)
# 详见nginx配置示例
```

## 维护和更新

### 定期维护
```bash
# 清理Docker资源
docker system prune -f

# 更新系统包
sudo apt update && sudo apt upgrade -y

# 检查日志大小
du -sh /var/lib/docker/containers/*/
```

### 服务更新
```bash
# 停止旧容器
docker stop paddleocr-ascend

# 拉取新镜像
docker load -i new-paddleocr-ascend-arm64.tar

# 启动新容器
./run_container.sh
```

## 联系支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目的troubleshooting.md文件
3. 查看PaddleOCR官方文档
4. 提交GitHub Issue
