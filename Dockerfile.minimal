FROM ubuntu:22.04
WORKDIR /app
ENV DEBIAN_FRONTEND=noninteractive
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list
RUN apt-get update && apt-get install -y python3 python3-pip curl && apt-get clean
RUN ln -sf /usr/bin/python3 /usr/bin/python
COPY requirements.ascend.txt app.py ocr_processor.py run.py .env ./
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
RUN pip install --no-cache-dir flask==3.0.0 numpy Pillow python-dotenv
RUN pip install --no-cache-dir paddle-custom-npu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/npu/
RUN pip install --no-cache-dir paddleocr==3.0.0
EXPOSE 9527
CMD ["python", "run.py"]
