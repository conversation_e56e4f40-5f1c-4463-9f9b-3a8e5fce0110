#!/bin/bash

echo "🚀 构建ARM64架构PaddleOCR Docker镜像"
echo "========================================"

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查模型文件是否存在
if [ ! -f "paddlex_models.tar.gz" ]; then
    echo "❌ 模型文件 paddlex_models.tar.gz 不存在"
    echo "请确保已将PaddleOCR模型文件放置在项目根目录"
    exit 1
fi

echo "✅ 检测到模型文件: paddlex_models.tar.gz"

# 设置镜像名称和标签
IMAGE_NAME="paddleocr-service"
IMAGE_TAG="arm64-latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "🔨 开始构建ARM64镜像..."
echo "镜像名称: ${FULL_IMAGE_NAME}"
echo "使用Dockerfile: Dockerfile.arm64"

# 构建ARM64镜像
if docker buildx build --platform linux/arm64 -f Dockerfile.arm64 -t "${FULL_IMAGE_NAME}" .; then
    echo "✅ ARM64镜像构建成功: ${FULL_IMAGE_NAME}"
else
    echo "❌ ARM64镜像构建失败"
    exit 1
fi

# 显示镜像信息
echo "📋 镜像信息:"
docker images | grep "${IMAGE_NAME}"

echo ""
echo "🎉 构建完成！"
echo ""
echo "📦 导出镜像命令:"
echo "  docker save ${FULL_IMAGE_NAME} -o paddleocr-service-arm64.tar"
echo ""
echo "🚀 在ARM64设备上加载镜像:"
echo "  docker load -i paddleocr-service-arm64.tar"
echo "  docker run -d -p 9527:9527 --name paddleocr-service ${FULL_IMAGE_NAME}"
echo ""
