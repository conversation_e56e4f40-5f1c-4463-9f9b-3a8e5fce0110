# 离线PaddleOCR部署指南

本指南提供了在无网络环境中部署PaddleOCR服务的完整流程，包含预下载的模型。

## 📋 概述

- **基础镜像**: 官方昇腾镜像 + 预下载模型
- **部署环境**: ARM64昇腾服务器（无网络连接）
- **特性**: 完全离线、包含所有模型、支持NPU加速

## 🚀 完整部署流程

### 阶段一：在有网环境中准备离线镜像

#### 1. 预下载模型（在当前容器中执行）

```bash
# 在有网络的容器中执行
echo "开始下载所有OCR模型..."

# 触发模型下载（运行不同类型的文件）
paddleocr ocr -i /work/tong.png --device npu

# 如果有PDF文件，也运行一次
# paddleocr ocr -i /work/test.pdf --device npu

# 检查下载的模型
ls -la /root/.paddlex/official_models/
du -sh /root/.paddlex/

# 打包模型文件
cd /root
tar -czf paddlex_models.tar.gz .paddlex/
ls -lh paddlex_models.tar.gz
```

#### 2. 导出模型包到宿主机

```bash
# 在宿主机上执行
docker cp <container_name>:/root/paddlex_models.tar.gz ./
```

#### 3. 构建离线镜像

```bash
# 确保所有文件都在当前目录
ls -la

# 应该包含以下文件：
# - Dockerfile.offline
# - paddlex_models.tar.gz
# - app_simple.py
# - ocr_subprocess.py
# - run_simple.py
# - requirements_simple.txt

# 给构建脚本执行权限
chmod +x build_offline_image.sh

# 构建离线镜像
./build_offline_image.sh
```

#### 4. 导出镜像用于离线部署

```bash
# 导出镜像为tar文件
docker save -o paddleocr-offline-ascend.tar paddleocr-offline-ascend:latest

# 检查导出的镜像文件
ls -lh paddleocr-offline-ascend.tar
```

### 阶段二：在无网环境中部署

#### 1. 传输文件到目标服务器

将以下文件传输到ARM昇腾服务器：
- `paddleocr-offline-ascend.tar` (镜像文件)
- `deploy_offline.sh` (部署脚本)

#### 2. 在目标服务器上导入镜像

```bash
# 导入镜像
docker load -i paddleocr-offline-ascend.tar

# 验证镜像导入
docker images | grep paddleocr-offline-ascend
```

#### 3. 部署服务

```bash
# 给部署脚本执行权限
chmod +x deploy_offline.sh

# 执行离线部署
./deploy_offline.sh
```

## 🧪 测试离线服务

### 1. 健康检查

```bash
curl http://localhost:9527/health
```

预期响应：
```json
{
  "status": "ok",
  "ocr_available": true,
  "device": "npu (via subprocess)"
}
```

### 2. 服务信息

```bash
curl http://localhost:9527/info
```

### 3. OCR功能测试

```bash
# 测试图片OCR
curl -X POST -F 'file=@test_image.png' http://localhost:9527/ocr

# 测试PDF OCR
curl -X POST -F 'file=@test_document.pdf' http://localhost:9527/ocr
```

## 📊 镜像信息

### 包含的模型
- **PP-LCNet_x1_0_doc_ori**: 文档方向分类模型
- **UVDoc**: 文档展平模型
- **PP-LCNet_x1_0_textline_ori**: 文本行方向分类模型
- **PP-OCRv5_server_det**: 文本检测模型
- **PP-OCRv5_server_rec**: 文本识别模型

### 预期镜像大小
- 基础镜像: ~8GB
- 模型文件: ~500MB-1GB
- 应用代码: <10MB
- **总计**: ~9-10GB

## 🔧 运维管理

### 查看服务状态

```bash
# 查看容器状态
docker ps | grep paddleocr-offline

# 查看服务日志
docker logs paddleocr-offline

# 实时查看日志
docker logs -f paddleocr-offline
```

### 服务管理

```bash
# 停止服务
docker stop paddleocr-offline

# 启动服务
docker start paddleocr-offline

# 重启服务
docker restart paddleocr-offline

# 进入容器调试
docker exec -it paddleocr-offline bash
```

### 性能监控

```bash
# 查看容器资源使用
docker stats paddleocr-offline

# 查看NPU使用情况（如果支持）
npu-smi info
```

## 🚨 故障排除

### 常见问题

1. **镜像导入失败**
   - 检查磁盘空间是否充足
   - 验证tar文件完整性

2. **容器启动失败**
   - 检查昇腾环境是否正确安装
   - 查看容器日志：`docker logs paddleocr-offline`

3. **OCR处理失败**
   - 检查文件格式是否支持
   - 验证文件大小是否超限

4. **NPU不可用**
   - 检查昇腾驱动安装
   - 验证设备文件权限

### 日志分析

关键日志信息：
- `✅ 找到rec_texts字段` - OCR解析成功
- `🚀 使用官方推荐的NPU模式` - NPU加速启用
- `📁 文件大小: X 字节` - 文件处理状态

## 📝 优势特性

✅ **完全离线**: 无需网络连接，包含所有必要模型
✅ **NPU加速**: 支持昇腾NPU硬件加速
✅ **开箱即用**: 一键部署，无需额外配置
✅ **高性能**: 基于subprocess调用，稳定可靠
✅ **易维护**: 标准Docker容器，便于管理

## 🔄 版本更新

如需更新模型或应用代码：
1. 在有网环境中重新构建镜像
2. 导出新的镜像文件
3. 在目标环境中重新部署

这种方式确保了完全的离线部署能力，适合对网络安全要求较高的生产环境。
