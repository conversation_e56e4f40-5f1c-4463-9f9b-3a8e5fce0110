# PaddleOCR 离线部署方案

## 概述

本项目提供了 PaddleOCR 的完整离线部署解决方案，支持在无网络环境下运行，所有模型文件都预先打包在 Docker 镜像中。

## 🎯 主要特性

- ✅ **离线部署**: 无需网络连接，模型文件预打包
- ✅ **ARM64支持**: 专门优化的ARM64架构镜像
- ✅ **跨平台构建**: Windows环境构建，ARM64环境部署
- ✅ **模型预加载**: 容器启动时直接使用预加载模型
- ✅ **NPU加速**: 支持昇腾NPU硬件加速
- ✅ **完整工具链**: 构建、部署、测试一体化脚本

## 📁 项目结构

```
paddleocr-docker/
├── 🐳 Docker文件
│   ├── Dockerfile              # 标准x86_64镜像
│   ├── Dockerfile.arm64        # ARM64专用镜像
│   └── Dockerfile.paddleocr    # PaddleOCR专用镜像
│
├── 🔨 构建脚本
│   ├── build_arm64.bat         # Windows ARM64构建脚本
│   ├── build_arm64.sh          # Linux ARM64构建脚本
│   ├── build_docker.bat        # 标准构建脚本
│   └── build_docker.sh         # 标准构建脚本
│
├── 🚀 部署脚本
│   ├── deploy_arm64.bat        # Windows部署脚本
│   └── deploy_arm64.sh         # Linux部署脚本
│
├── 🧪 测试工具
│   ├── test_deployment.py      # 部署验证脚本
│   ├── test_ocr.py            # OCR功能测试
│   └── test_client.py         # 客户端测试
│
├── 📚 文档
│   ├── ARM64_DEPLOYMENT_GUIDE.md    # ARM64部署完整指南
│   ├── README_OFFLINE_DEPLOYMENT.md # 离线部署说明
│   └── troubleshooting.md           # 故障排除指南
│
├── 🧠 核心代码
│   ├── app.py                 # Flask应用主文件
│   ├── ocr_processor.py       # OCR处理器（支持模型预加载）
│   ├── ocr_utils.py          # OCR工具类
│   └── run.py                # 启动脚本
│
└── 📦 模型文件
    └── paddlex_models.tar.gz  # 预训练模型包
```

## 🚀 快速开始

### 方案A: ARM64服务器本地构建（推荐）

适用于直接在ARM64服务器上构建和部署的场景。

#### 1. 准备模型文件
确保项目根目录包含 `paddlex_models.tar.gz` 文件，其中包含以下模型：
- PP-LCNet_x1_0_doc_ori
- PP-LCNet_x1_0_textline_ori
- PP-OCRv5_server_det
- PP-OCRv5_server_rec
- UVDoc

#### 2. 一键部署
```bash
# 给脚本执行权限
chmod +x one_click_deploy.sh

# 运行一键部署脚本
./one_click_deploy.sh
```

#### 3. 或分步执行
```bash
# 步骤1: 构建镜像
chmod +x build_local_arm64.sh
./build_local_arm64.sh

# 步骤2: 部署服务
chmod +x deploy_arm64.sh
./deploy_arm64.sh

# 步骤3: 验证部署
python3 test_deployment.py
```

### 方案B: 跨平台构建（Windows构建，ARM64部署）

适用于在Windows环境构建，然后部署到ARM64服务器的场景。

#### 1. 在Windows环境构建ARM64镜像
```cmd
# 运行构建脚本
build_arm64.bat
```

#### 2. 导出镜像
```cmd
# 导出为tar文件，便于传输
docker save paddleocr-service:arm64-latest -o paddleocr-service-arm64.tar
```

#### 3. 部署到ARM64服务器
```bash
# 传输镜像文件到目标服务器
scp paddleocr-service-arm64.tar user@arm64-server:/tmp/

# 在目标服务器上部署
./deploy_arm64.sh --load-only
```

#### 4. 验证部署
```bash
# 运行测试脚本
python3 test_deployment.py
```

## 🔧 详细使用说明

### Windows环境构建

1. **检查环境**
   ```cmd
   # 检查Docker版本
   docker --version
   
   # 检查BuildKit支持
   docker buildx version
   ```

2. **构建镜像**
   ```cmd
   # 自动构建（推荐）
   build_arm64.bat
   
   # 手动构建
   docker buildx build --platform linux/arm64 -f Dockerfile.arm64 -t paddleocr-service:arm64-latest .
   ```

3. **导出镜像**
   ```cmd
   docker save paddleocr-service:arm64-latest -o paddleocr-service-arm64.tar
   ```

### Linux环境部署

1. **传输镜像**
   ```bash
   # 网络传输
   scp paddleocr-service-arm64.tar user@target-server:/tmp/
   
   # 或使用物理介质（U盘等）
   ```

2. **部署服务**
   ```bash
   # 自动部署（推荐）
   chmod +x deploy_arm64.sh
   ./deploy_arm64.sh
   
   # 手动部署
   docker load -i paddleocr-service-arm64.tar
   docker run -d --name paddleocr-service -p 9527:9527 paddleocr-service:arm64-latest
   ```

3. **验证部署**
   ```bash
   # 健康检查
   curl http://localhost:9527/health
   
   # 完整测试
   python3 test_deployment.py
   ```

## 🏥 健康检查和监控

### 基本检查

```bash
# 检查容器状态
docker ps | grep paddleocr-service

# 查看容器日志
docker logs paddleocr-service

# 健康检查接口
curl http://localhost:9527/health
```

### 性能监控

```bash
# 查看资源使用情况
docker stats paddleocr-service

# 查看详细信息
docker inspect paddleocr-service
```

## 🔍 API使用说明

### 健康检查接口

```bash
GET /health

# 响应示例
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### OCR识别接口

```bash
POST /ocr
Content-Type: multipart/form-data

# 参数
file: 图片或PDF文件

# 响应示例
{
  "texts": [
    ["第一页文本1", "第一页文本2"],
    ["第二页文本1", "第二页文本2"]
  ]
}
```

### 服务信息接口

```bash
GET /

# 响应示例
{
  "service": "PaddleOCR服务",
  "version": "1.0.0",
  "supported_formats": [".pdf", ".png", ".jpg", ".jpeg"]
}
```

## 🛠️ 故障排除

### 常见问题

1. **模型加载失败**
   ```bash
   # 检查模型目录
   docker exec paddleocr-service ls -la /root/.paddleocr/
   
   # 检查环境变量
   docker exec paddleocr-service env | grep PADDLE
   ```

2. **内存不足**
   ```bash
   # 增加内存限制
   docker update --memory 8g paddleocr-service
   ```

3. **端口冲突**
   ```bash
   # 使用不同端口
   docker run -p 9528:9527 paddleocr-service:arm64-latest
   ```

### 日志分析

```bash
# 查看启动日志
docker logs paddleocr-service --since 10m

# 实时日志
docker logs -f paddleocr-service

# 详细日志
docker logs paddleocr-service --details
```

## 📊 性能优化

### 资源配置

```bash
# 生产环境推荐配置
docker run -d \
  --name paddleocr-service \
  -p 9527:9527 \
  --restart unless-stopped \
  --memory 8g \
  --cpus 4 \
  --shm-size 2g \
  paddleocr-service:arm64-latest
```

### NPU加速

如果ARM64服务器支持昇腾NPU：

```bash
# 挂载NPU设备
docker run -d \
  --name paddleocr-service \
  -p 9527:9527 \
  --device /dev/davinci0 \
  --device /dev/davinci_manager \
  -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
  paddleocr-service:arm64-latest
```

## 🔄 更新和维护

### 更新镜像

```bash
# 停止旧容器
docker stop paddleocr-service

# 删除旧容器
docker rm paddleocr-service

# 加载新镜像
docker load -i new-paddleocr-service-arm64.tar

# 启动新容器
./deploy_arm64.sh
```

### 备份和恢复

```bash
# 备份镜像
docker save paddleocr-service:arm64-latest | gzip > paddleocr-backup.tar.gz

# 恢复镜像
gunzip -c paddleocr-backup.tar.gz | docker load
```

## 📞 技术支持

如果遇到问题，请按以下步骤排查：

1. 查看 [故障排除指南](troubleshooting.md)
2. 检查容器日志：`docker logs paddleocr-service`
3. 运行测试脚本：`python3 test_deployment.py`
4. 查看系统资源：`docker stats paddleocr-service`

## 📝 更新日志

- **v1.0.0**: 初始版本，支持基本OCR功能
- **v1.1.0**: 添加ARM64支持和模型预加载
- **v1.2.0**: 添加NPU加速支持
- **v1.3.0**: 完善离线部署方案和工具链

---

🎉 **恭喜！您已成功部署PaddleOCR离线服务！**
