#!/bin/bash

# 重新构建离线PaddleOCR镜像脚本
# 清理旧镜像并使用正确的版本重新构建

set -e

echo "========================================="
echo "重新构建离线PaddleOCR镜像"
echo "使用正确的稳定版本"
echo "========================================="
echo

# 设置镜像信息
IMAGE_NAME="paddleocr-offline-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="paddleocr-offline"

echo "🧹 清理现有容器和镜像..."

# 停止并删除现有容器
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "停止容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    echo "删除容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 删除现有镜像
if docker images | grep -q "$IMAGE_NAME"; then
    echo "删除镜像: $FULL_IMAGE_NAME"
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
fi

echo "✅ 清理完成"
echo

# 检查必要文件
REQUIRED_FILES=(
    "Dockerfile.offline"
    "paddlex_models.tar.gz"
    "app_simple.py"
    "ocr_subprocess.py"
    "run_simple.py"
    "requirements_simple.txt"
)

echo "🔍 检查必要文件..."
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ 找到文件: $file"
    fi
done

# 检查模型文件大小
MODEL_SIZE=$(du -h paddlex_models.tar.gz | cut -f1)
echo "📦 模型文件大小: $MODEL_SIZE"

echo
echo "🚀 开始重新构建镜像: $FULL_IMAGE_NAME"
echo "使用稳定版本:"
echo "  - PaddlePaddle: 3.1.0"
echo "  - paddle-custom-npu: 3.1.0"
echo "  - PaddleOCR: 3.1.0"
echo "  - NumPy: 1.26.4"
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

# 构建Docker镜像（兼容旧版本Docker）
docker build \
    --platform linux/arm64 \
    -f Dockerfile.offline \
    -t "$FULL_IMAGE_NAME" \
    --no-cache \
    .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ 离线镜像重新构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images "$IMAGE_NAME"
    echo
    
    # 验证镜像中的版本
    echo "🔍 验证镜像中的版本..."
    docker run --rm "$FULL_IMAGE_NAME" python -c "
import paddle
import paddleocr
print('✅ PaddlePaddle version:', paddle.__version__)
print('✅ PaddleOCR version:', paddleocr.__version__)
try:
    import paddle_custom_device
    print('✅ paddle-custom-npu: installed')
except ImportError as e:
    print('⚠️  paddle-custom-npu:', e)
"
    
    echo
    echo "🎉 重新构建完成!"
    echo
    echo "📋 后续步骤:"
    echo
    echo "1. 启动离线服务:"
    echo "   ./deploy_offline.sh"
    echo
    echo "2. 或手动启动:"
    echo "   docker run -d --name paddleocr-offline \\"
    echo "     --privileged --network=host --shm-size=128G \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo
    echo "3. 测试服务:"
    echo "   curl http://localhost:9527/health"
    echo "   curl -X POST -F 'file=@test_image.jpg' http://localhost:9527/ocr"
    echo
    echo "4. 导出镜像用于其他服务器:"
    echo "   docker save -o paddleocr-offline-ascend-fixed.tar $FULL_IMAGE_NAME"
    
else
    echo
    echo "❌ 镜像重新构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo "请检查构建日志中的错误信息"
    exit 1
fi
