#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版PaddleOCR服务启动脚本
"""

import logging
import sys
from app_simple import app

if __name__ == '__main__':
    # 配置日志输出
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 设置Flask日志
    app.logger.setLevel(logging.INFO)

    print("🚀 启动简化版PaddleOCR服务...")
    print("📍 服务地址: http://0.0.0.0:9527")
    print("🔗 健康检查: http://localhost:9527/health")
    print("🔗 服务信息: http://localhost:9527/info")
    print("🔗 OCR接口: http://localhost:9527/ocr (POST)")
    print("📝 日志级别: INFO")

    # 启动Flask应用（启用调试信息但不启用调试模式）
    app.run(host='0.0.0.0', port=9527, debug=False, use_reloader=False)
