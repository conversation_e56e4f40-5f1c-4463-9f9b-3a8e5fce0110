#!/bin/bash

# 快速修复和重新部署脚本
# 修复PaddleOCR 3.0.0兼容性问题后重新部署

echo "🔧 PaddleOCR兼容性问题快速修复"
echo "================================"

# 停止旧容器
echo "🛑 停止旧容器..."
docker stop paddleocr-service 2>/dev/null || true
docker rm paddleocr-service 2>/dev/null || true

# 删除旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 重新构建镜像
echo "🔨 重新构建镜像..."
if docker build -f Dockerfile.local-arm64 -t paddleocr-service:arm64-latest .; then
    echo "✅ 镜像重新构建成功"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 重新部署
echo "🚀 重新部署服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 服务重新部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 20
    exit 1
fi

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..10}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/10)"
        sleep 5
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 健康检查失败"
        echo "容器日志:"
        docker logs paddleocr-service --tail 20
        exit 1
    fi
done

echo ""
echo "🎉 修复和重新部署完成！"
echo "========================"
echo "服务地址: http://localhost:9527"
echo "健康检查: curl http://localhost:9527/health"
echo "查看日志: docker logs paddleocr-service"
echo ""

# 显示容器状态
docker ps | grep paddleocr-service
