@echo off
REM 昇腾NPU版本Docker手动构建脚本 (Windows)
REM 适用于在Windows上构建ARM64架构的昇腾NPU Docker镜像

echo =========================================
echo PaddleOCR 昇腾NPU版本 Docker手动构建脚本
echo =========================================
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "Dockerfile.ascend" (
    echo ❌ 错误: 缺少 Dockerfile.ascend 文件
    pause
    exit /b 1
)

if not exist "requirements.ascend.txt" (
    echo ❌ 错误: 缺少 requirements.ascend.txt 文件
    pause
    exit /b 1
)

if not exist "app.py" (
    echo ❌ 错误: 缺少 app.py 文件
    pause
    exit /b 1
)

if not exist "ocr_processor.py" (
    echo ❌ 错误: 缺少 ocr_processor.py 文件
    pause
    exit /b 1
)

if not exist "run.py" (
    echo ❌ 错误: 缺少 run.py 文件
    pause
    exit /b 1
)

if not exist ".env" (
    echo ❌ 错误: 缺少 .env 文件
    pause
    exit /b 1
)

echo ✓ 所有必要文件检查完成
echo.

REM 检查昇腾CANN工具包
if not exist "Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run" (
    echo ⚠️  警告: 未找到昇腾CANN工具包文件
    echo    Docker镜像将不包含CANN工具包，需要在宿主机上安装
    echo    或者将CANN工具包文件放置在项目根目录
    echo.
)

REM 设置镜像信息
set IMAGE_NAME=paddleocr-arm64-server
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%

echo 开始构建Docker镜像: %FULL_IMAGE_NAME%
echo 构建上下文: %CD%
echo.

REM 显示将要执行的命令
echo 执行构建命令:
echo docker build --platform linux/arm64 -f Dockerfile.ascend -t %FULL_IMAGE_NAME% --no-cache --progress=plain .
echo.

REM 构建Docker镜像
docker build --platform linux/arm64 -f Dockerfile.ascend -t %FULL_IMAGE_NAME% --no-cache --progress=plain .

if %errorlevel% equ 0 (
    echo.
    echo ✅ Docker镜像构建成功!
    echo 镜像名称: %FULL_IMAGE_NAME%
    echo.
    
    REM 显示镜像信息
    echo 镜像信息:
    docker images %IMAGE_NAME%
    echo.
    
    echo 后续步骤:
    echo 1. 运行容器 ^(昇腾NPU模式^):
    echo    docker run --name paddleocr-ascend -p 9527:9527 \
    echo      --device=/dev/davinci0:/dev/davinci0 \
    echo      --device=/dev/davinci_manager:/dev/davinci_manager \
    echo      --device=/dev/devmm_svm:/dev/devmm_svm \
    echo      --device=/dev/hisi_hdc:/dev/hisi_hdc \
    echo      -v /usr/local/Ascend:/usr/local/Ascend:ro \
    echo      -d %FULL_IMAGE_NAME%
    echo.
    echo 2. 测试服务:
    echo    curl http://localhost:9527/health
    echo.
    echo 3. 如果在Windows上测试，请使用:
    echo    Invoke-WebRequest -Uri http://localhost:9527/health
    
) else (
    echo.
    echo ❌ Docker镜像构建失败!
    echo 请检查构建日志中的错误信息
    echo.
    echo 常见问题解决方案:
    echo 1. 网络问题: 检查网络连接，可能需要配置代理
    echo 2. 权限问题: 确保Docker有足够权限
    echo 3. 磁盘空间: 检查磁盘空间是否充足
    echo 4. 依赖问题: 检查requirements.ascend.txt中的依赖版本
    pause
    exit /b 1
)

pause
