#!/bin/bash

# Mobile模型部署脚本（无图片尺寸限制）
# 使用mobile模型提高ARM64兼容性，但不限制图片尺寸

echo "📱 PaddleOCR Mobile模型部署"
echo "=========================="

echo "📋 配置说明:"
echo "  模型类型: Mobile模型（更好的ARM64兼容性）"
echo "  图片尺寸: 无限制（根据用户需求）"
echo "  CPU线程: 单线程（避免冲突）"
echo "  MKL-DNN: 禁用（ARM64优化）"
echo ""

# 检查Docker环境
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查网络连接
echo "🌐 检查网络连接..."
if curl -s --connect-timeout 5 https://www.baidu.com >/dev/null; then
    echo "✅ 网络连接正常"
else
    echo "⚠️ 网络连接可能有问题，但继续部署"
fi

# 检查必要文件
required_files=("app.py" "ocr_processor.py" "run.py" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 项目文件检查通过"

# 显示当前版本配置
echo "📦 当前依赖版本:"
grep -E "(paddlepaddle|paddleocr|numpy)" requirements.txt

# 询问是否继续
echo ""
echo "🚀 是否开始部署Mobile模型版本？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消部署"
    exit 0
fi

# 停止旧容器
if docker ps | grep -q paddleocr-service; then
    echo "🛑 停止旧容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 备份旧镜像
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    echo "💾 备份旧镜像..."
    docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-server-backup 2>/dev/null || true
    echo "✅ 旧镜像已备份"
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 构建Mobile模型版本镜像
echo ""
echo "🔨 构建Mobile模型版本镜像..."
echo "使用mobile模型，无图片尺寸限制"
echo "这可能需要 10-30 分钟，请耐心等待..."

start_time=$(date +%s)

if docker build -f Dockerfile.online -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ Mobile模型版本镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 显示镜像信息
image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "paddleocr-service" | grep "arm64-latest" | awk '{print $3}')
echo "📦 镜像大小: ${image_size}"

# 部署Mobile模型服务
echo ""
echo "🚀 部署Mobile模型服务..."
echo "无图片尺寸限制，支持大图片处理"

docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 6g \
    --cpus 2 \
    --shm-size 1g \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ Mobile模型服务部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
echo "注意: 首次启动时会下载mobile模型"
sleep 30

# 检查容器状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 显示启动日志
echo ""
echo "📋 服务启动日志:"
docker logs paddleocr-service --tail 15

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..20}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/20)"
        sleep 15
    fi
    
    if [ $i -eq 20 ]; then
        echo "❌ 健康检查超时"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 验证版本信息
echo ""
echo "🔍 验证版本信息..."
docker exec paddleocr-service python -c "
try:
    import paddle
    print(f'✅ PaddlePaddle版本: {paddle.__version__}')
    
    import paddleocr
    print('✅ PaddleOCR 3.0.0 导入成功')
    
    import numpy
    print(f'✅ NumPy版本: {numpy.__version__}')
    
    print('✅ Mobile模型配置已应用')
except Exception as e:
    print(f'❌ 验证失败: {e}')
"

# 测试服务信息
echo ""
echo "ℹ️ 获取服务信息..."
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ 服务信息:')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
    else:
        print('❌ 服务信息获取失败')
except Exception as e:
    print(f'⚠️ 服务信息获取异常: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法获取服务信息，但服务可能仍在初始化中"
fi

echo ""
echo "🎉 Mobile模型部署完成！"
echo "======================="
echo "✅ PaddleOCR Mobile模型已部署"
echo "✅ 无图片尺寸限制，支持大图片处理"
echo "✅ 使用单线程配置，提高稳定性"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "📱 Mobile模型特性:"
echo "   - 更好的ARM64兼容性"
echo "   - 更小的模型体积，下载更快"
echo "   - 支持任意尺寸图片"
echo "   - 精度略低于Server模型，但稳定性更好"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   python3 test_deployment.py"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: docker logs paddleocr-service"
echo "   重启服务: docker restart paddleocr-service"
echo "   查看资源: docker stats paddleocr-service"
echo ""
echo "⚠️ 重要提醒:"
echo "   - Mobile模型精度略低于Server模型"
echo "   - 如需更高精度，可以考虑使用Server模型"
echo "   - 如果出现段错误，可以考虑限制图片尺寸"
echo ""

echo "✅ 部署流程完成！"
