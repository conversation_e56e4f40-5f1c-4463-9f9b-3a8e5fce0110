#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版PaddleOCR服务测试客户端
"""

import requests
import json
import sys
import os

def test_health():
    """测试健康检查接口"""
    try:
        response = requests.get("http://localhost:9527/health")
        print(f"健康检查状态码: {response.status_code}")
        print(f"健康检查响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_info():
    """测试服务信息接口"""
    try:
        response = requests.get("http://localhost:9527/info")
        print(f"服务信息状态码: {response.status_code}")
        print(f"服务信息响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"服务信息获取失败: {e}")
        return False

def test_ocr(file_path):
    """测试OCR识别接口"""
    if not os.path.exists(file_path):
        print(f"测试文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post("http://localhost:9527/ocr", files=files)
        
        print(f"OCR识别状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("OCR识别成功!")
            print(f"识别结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 提取并显示文本内容
            if 'texts' in result and result['texts']:
                print("\n📄 提取的文本内容:")
                for page_idx, page_texts in enumerate(result['texts']):
                    print(f"  第{page_idx + 1}页:")
                    for text in page_texts:
                        print(f"    - {text}")
            else:
                print("⚠️  未提取到文本内容")
        else:
            print(f"OCR识别失败: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"OCR测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试简化版PaddleOCR服务...")
    print("=" * 50)
    
    # 测试健康检查
    print("1. 测试健康检查接口...")
    health_ok = test_health()
    print()
    
    # 测试服务信息
    print("2. 测试服务信息接口...")
    info_ok = test_info()
    print()
    
    # 测试OCR功能
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        print(f"3. 测试OCR识别接口 (文件: {test_file})...")
        ocr_ok = test_ocr(test_file)
    else:
        print("3. 跳过OCR测试 (未提供测试文件)")
        print("   使用方法: python test_simple_client.py <image_or_pdf_path>")
        ocr_ok = True
    
    print()
    print("=" * 50)
    print("📊 测试结果总结:")
    print(f"   健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"   服务信息: {'✅ 通过' if info_ok else '❌ 失败'}")
    if len(sys.argv) > 1:
        print(f"   OCR识别: {'✅ 通过' if ocr_ok else '❌ 失败'}")
    
    if health_ok and info_ok:
        print("\n🎉 基础功能测试通过!")
        if len(sys.argv) > 1 and ocr_ok:
            print("🎉 OCR功能测试也通过!")
    else:
        print("\n⚠️  部分测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
