@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo =========================================
echo PaddleOCR 昇腾NPU版本 Docker构建脚本 (Windows)
echo =========================================
echo.

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

echo ✓ Docker运行状态正常
echo.

REM 检查必要文件
set "REQUIRED_FILES=Dockerfile.ascend requirements.ascend.txt app.py ocr_processor.py run.py"
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        echo ❌ 错误: 缺少必要文件: %%f
        pause
        exit /b 1
    )
)

echo ✓ 所有必要文件检查完成
echo.

REM 检查昇腾CANN工具包
set "CANN_FILE=Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run"
if not exist "%CANN_FILE%" (
    echo ⚠️  警告: 未找到昇腾CANN工具包文件: %CANN_FILE%
    echo    Docker镜像将不包含CANN工具包，需要在目标服务器上安装
    echo    或者将CANN工具包文件放置在项目根目录
    echo.
)

REM 设置镜像信息
set "IMAGE_NAME=paddleocr-ascend"
set "IMAGE_TAG=latest"
set "FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%"

echo 开始构建Docker镜像: %FULL_IMAGE_NAME%
echo 构建上下文: %CD%
echo.
echo 注意: 在Windows上构建ARM64镜像，需要启用Docker Desktop的实验性功能
echo.

REM 构建Docker镜像
echo 执行构建命令...
docker build --platform linux/arm64 -f Dockerfile.ascend -t "%FULL_IMAGE_NAME%" --progress=plain .

if %errorlevel% eq 0 (
    echo.
    echo ✅ Docker镜像构建成功!
    echo 镜像名称: %FULL_IMAGE_NAME%
    echo.
    
    REM 显示镜像信息
    echo 镜像信息:
    docker images %IMAGE_NAME%
    echo.
    
    echo 后续步骤:
    echo 1. 将镜像保存为tar文件传输到ARM64服务器:
    echo    docker save %FULL_IMAGE_NAME% -o paddleocr-ascend-arm64.tar
    echo.
    echo 2. 在ARM64服务器上加载镜像:
    echo    docker load -i paddleocr-ascend-arm64.tar
    echo.
    echo 3. 在ARM64服务器上运行容器:
    echo    ./run_container.sh
    echo.
    echo 4. 或使用Docker Compose运行:
    echo    docker-compose -f docker-compose.ascend.yml up -d
    echo.
    
    REM 询问是否保存镜像
    set /p "SAVE_IMAGE=是否现在保存镜像为tar文件? (y/N): "
    if /i "!SAVE_IMAGE!"=="y" (
        echo.
        echo 正在保存镜像...
        docker save %FULL_IMAGE_NAME% -o paddleocr-ascend-arm64.tar
        if !errorlevel! eq 0 (
            echo ✅ 镜像已保存为: paddleocr-ascend-arm64.tar
            echo 文件大小:
            dir paddleocr-ascend-arm64.tar | findstr paddleocr-ascend-arm64.tar
        ) else (
            echo ❌ 镜像保存失败
        )
    )
    
) else (
    echo.
    echo ❌ Docker镜像构建失败!
    echo 请检查构建日志中的错误信息
    echo.
    echo 常见问题:
    echo 1. 确保Docker Desktop已启用实验性功能
    echo 2. 确保有足够的磁盘空间
    echo 3. 检查网络连接是否正常
    pause
    exit /b 1
)

echo.
pause
