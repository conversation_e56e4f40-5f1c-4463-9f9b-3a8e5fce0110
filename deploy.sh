#!/bin/bash

# PaddleOCR Docker部署脚本
# 自动检测硬件环境并选择合适的版本配置

set -e

echo "========================================="
echo "PaddleOCR Docker部署脚本"
echo "========================================="
echo

# 检测系统架构
ARCH=$(uname -m)
echo "检测到系统架构: $ARCH"

# 检测硬件环境
detect_hardware() {
    echo "正在检测硬件环境..."
    
    # 检测昇腾NPU设备
    ASCEND_DEVICES=""
    for i in {0..7}; do
        if [ -e "/dev/davinci$i" ]; then
            ASCEND_DEVICES="$ASCEND_DEVICES $i"
        fi
    done
    
    # 检测NVIDIA GPU
    NVIDIA_GPU=""
    if command -v nvidia-smi &> /dev/null; then
        NVIDIA_GPU=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null | head -1)
    fi
    
    echo "硬件检测结果:"
    if [ -n "$ASCEND_DEVICES" ]; then
        echo "  ✓ 检测到昇腾NPU设备: $ASCEND_DEVICES"
        HARDWARE_TYPE="ascend"
    elif [ -n "$NVIDIA_GPU" ]; then
        echo "  ✓ 检测到NVIDIA GPU: $NVIDIA_GPU"
        HARDWARE_TYPE="nvidia"
    else
        echo "  ⚠ 未检测到GPU/NPU设备，将使用CPU模式"
        HARDWARE_TYPE="cpu"
    fi
    echo
}

# 显示版本信息
show_version_info() {
    echo "========================================="
    echo "版本配置信息"
    echo "========================================="
    
    case $HARDWARE_TYPE in
        "ascend")
            echo "昇腾NPU环境 - 用户指定版本组合:"
            echo "  - paddlepaddle: 3.1.0"
            echo "  - paddle-custom-npu: 3.1.0"
            echo "  - paddleocr: 3.0.0"
            echo "  - CANN: 8.0.RC1"
            echo "  - 配置文件: Dockerfile.ascend, requirements.ascend.txt"
            ;;
        "nvidia")
            echo "NVIDIA GPU环境 - 推荐版本组合:"
            echo "  - paddlepaddle-gpu: 3.0.0"
            echo "  - paddleocr: 3.0.0"
            echo "  - CUDA: 12.6"
            echo "  - 配置文件: Dockerfile, requirements.txt"
            ;;
        "cpu")
            echo "CPU环境 - 基础版本组合:"
            echo "  - paddlepaddle: 3.0.0"
            echo "  - paddleocr: 3.0.0"
            echo "  - 配置文件: Dockerfile, requirements.txt"
            ;;
    esac
    echo
}

# 构建Docker镜像
build_docker() {
    echo "========================================="
    echo "构建Docker镜像"
    echo "========================================="
    
    case $HARDWARE_TYPE in
        "ascend")
            echo "构建昇腾NPU版本镜像..."
            
            # 检查CANN工具包文件
            CANN_FILE="Ascend-cann-toolkit_8.0.RC1_linux-aarch64.run"
            if [ ! -f "$CANN_FILE" ]; then
                echo "⚠ 警告: 未找到CANN工具包文件 $CANN_FILE"
                echo "请从华为昇腾官网下载CANN 8.0.RC1工具包文件到项目根目录"
                echo "下载地址: https://www.hiascend.com/software/cann"
                echo "或者确保宿主机已安装CANN 8.0.RC1工具包"
                echo
                read -p "是否继续构建? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    echo "构建已取消"
                    exit 1
                fi
            fi
            
            docker build -f Dockerfile.ascend -t paddleocr-ascend:stable .
            IMAGE_NAME="paddleocr-ascend:stable"
            COMPOSE_FILE="docker-compose.ascend.yml"
            ;;
        "nvidia"|"cpu")
            echo "构建GPU/CPU版本镜像..."
            docker build -f Dockerfile -t paddleocr-gpu:latest .
            IMAGE_NAME="paddleocr-gpu:latest"
            COMPOSE_FILE="docker-compose.yml"
            ;;
    esac
    
    echo "✓ Docker镜像构建完成: $IMAGE_NAME"
    echo
}

# 启动服务
start_service() {
    echo "========================================="
    echo "启动服务"
    echo "========================================="
    
    echo "使用配置文件: $COMPOSE_FILE"
    
    # 停止现有服务
    if [ -f "$COMPOSE_FILE" ]; then
        echo "停止现有服务..."
        docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
    fi
    
    # 启动新服务
    echo "启动新服务..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    echo "✓ 服务启动完成"
    echo
    
    # 显示服务状态
    echo "服务状态:"
    docker-compose -f "$COMPOSE_FILE" ps
    echo
    
    echo "服务访问地址: http://localhost:9527"
    echo "健康检查: http://localhost:9527/health"
}

# 显示部署后信息
show_post_deploy_info() {
    echo "========================================="
    echo "部署完成"
    echo "========================================="
    
    echo "服务信息:"
    echo "  - 访问地址: http://localhost:9527"
    echo "  - 健康检查: http://localhost:9527/health"
    echo "  - 日志查看: docker-compose -f $COMPOSE_FILE logs -f"
    echo
    
    case $HARDWARE_TYPE in
        "ascend")
            echo "昇腾NPU特殊说明:"
            echo "  - 确保宿主机已安装昇腾驱动和CANN工具包"
            echo "  - 运行 ./check_ascend_devices.sh 检测NPU设备"
            echo "  - 使用 python diagnose_paddle.py 验证环境"
            ;;
        "nvidia")
            echo "NVIDIA GPU特殊说明:"
            echo "  - 确保宿主机已安装NVIDIA驱动和Docker GPU支持"
            echo "  - 检查GPU状态: nvidia-smi"
            ;;
    esac
    echo
    
    echo "故障排除:"
    echo "  - 查看详细日志: docker-compose -f $COMPOSE_FILE logs"
    echo "  - 重启服务: docker-compose -f $COMPOSE_FILE restart"
    echo "  - 查看版本兼容性: cat VERSION_COMPATIBILITY.md"
    echo
}

# 主函数
main() {
    detect_hardware
    show_version_info
    
    # 确认部署
    read -p "是否继续部署? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        echo "部署已取消"
        exit 0
    fi
    
    build_docker
    start_service
    show_post_deploy_info
}

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装"
    echo "请先安装Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ 错误: Docker Compose未安装"
    echo "请先安装Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
fi

# 运行主函数
main
