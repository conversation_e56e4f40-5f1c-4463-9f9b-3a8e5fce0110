#!/bin/bash

echo "启动PaddleOCR ARM64容器..."

# 检查可用的镜像
CONTAINER_NAME="paddleocr-arm64-server"
IMAGE_NAME=""

# 优先使用本地构建的镜像
if docker images | grep -q "paddleocr-arm64-server"; then
    IMAGE_NAME="paddleocr-arm64-server:latest"
    echo "使用本地构建镜像: $IMAGE_NAME"
elif docker images | grep -q "paddleocr-ascend"; then
    IMAGE_NAME="paddleocr-ascend:latest"
    echo "使用昇腾镜像: $IMAGE_NAME"
else
    echo "❌ 错误: 未找到可用的Docker镜像"
    echo "请先构建镜像:"
    echo "  - 本地构建: ./build_arm64_server.sh"
    echo "  - 或加载镜像: docker load -i paddleocr-ascend-arm64.tar"
    exit 1
fi

# 停止并删除旧容器
echo "停止旧容器..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# 检查是否有昇腾设备
echo "检查昇腾NPU设备..."

# 检测所有可能的昇腾设备
DAVINCI_DEVICES=""
DEVICE_ID=""
for i in {0..7}; do
    if [ -e "/dev/davinci$i" ]; then
        echo "✓ 检测到昇腾NPU设备: /dev/davinci$i"
        DAVINCI_DEVICES="$DAVINCI_DEVICES --device=/dev/davinci$i:/dev/davinci$i"
        if [ -z "$DEVICE_ID" ]; then
            DEVICE_ID="$i"  # 使用第一个找到的设备作为主设备
        fi
    fi
done

if [ -n "$DAVINCI_DEVICES" ]; then
    echo "✓ 检测到昇腾NPU设备，启用设备挂载..."
    echo "主设备ID: $DEVICE_ID"

    # 检查昇腾软件栈
    if [ -d "/usr/local/Ascend" ]; then
        echo "✓ 检测到昇腾软件栈，挂载驱动库..."
        ASCEND_MOUNT="-v /usr/local/Ascend:/usr/local/Ascend:ro"
    else
        echo "⚠️  警告: 未检测到昇腾软件栈，容器内需要包含CANN工具包"
        ASCEND_MOUNT=""
    fi

    # 检查其他必要的设备文件
    EXTRA_DEVICES=""
    for device in davinci_manager devmm_svm hisi_hdc; do
        if [ -e "/dev/$device" ]; then
            EXTRA_DEVICES="$EXTRA_DEVICES --device=/dev/$device:/dev/$device"
            echo "✓ 检测到设备: /dev/$device"
        else
            echo "⚠️  警告: 未检测到设备: /dev/$device"
        fi
    done

    docker run --name $CONTAINER_NAME -p 9527:9527 \
        $DAVINCI_DEVICES \
        $EXTRA_DEVICES \
        $ASCEND_MOUNT \
        -e ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp \
        -e ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest \
        -e LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64 \
        -e FLAGS_selected_npus=$DEVICE_ID \
        --restart=unless-stopped \
        -d $IMAGE_NAME
else
    echo "⚠️  未检测到昇腾设备，使用CPU模式..."
    docker run --name $CONTAINER_NAME -p 9527:9527 \
        --restart=unless-stopped \
        -d $IMAGE_NAME
fi

# 检查容器状态
echo "等待容器启动..."
sleep 5

if docker ps | grep $CONTAINER_NAME >/dev/null; then
    echo "✅ 容器启动成功"
    echo "服务地址: http://localhost:9527"
    echo "健康检查: curl http://localhost:9527/health"
    echo
    echo "实用命令:"
    echo "  查看日志: docker logs $CONTAINER_NAME"
    echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
    echo "  停止容器: docker stop $CONTAINER_NAME"
    echo "  重启容器: docker restart $CONTAINER_NAME"

    # 等待服务启动并进行健康检查
    echo
    echo "等待服务启动..."
    sleep 10

    if curl -f http://localhost:9527/health >/dev/null 2>&1; then
        echo "✅ 服务健康检查通过"
    else
        echo "⚠️  服务健康检查失败，请查看日志: docker logs $CONTAINER_NAME"
    fi
else
    echo "❌ 容器启动失败"
    echo "查看错误日志: docker logs $CONTAINER_NAME"
    echo
    echo "常见问题排查:"
    echo "1. 检查端口是否被占用: netstat -tlnp | grep 9527"
    echo "2. 检查镜像是否存在: docker images"
    echo "3. 检查Docker服务状态: sudo systemctl status docker"
    exit 1
fi
