#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR子进程处理器
使用subprocess调用paddleocr命令行工具进行OCR识别
"""

import os
import subprocess
import json
import re
from typing import List, Dict, Any, Optional

class OCRSubprocessor:
    def __init__(self):
        """初始化OCR子进程处理器"""
        self.ocr_command = "paddleocr"
        self.device = "npu"  # 默认使用NPU设备
        self.timeout = 300   # 命令执行超时时间（秒）
        
        print("🔧 初始化OCR子进程处理器")
        print(f"   命令: {self.ocr_command}")
        print(f"   设备: {self.device}")
        print(f"   超时: {self.timeout}秒")

    def test_ocr_command(self) -> bool:
        """测试OCR命令是否可用"""
        try:
            # 测试paddleocr命令是否存在
            result = subprocess.run(
                [self.ocr_command, "--help"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("✅ paddleocr命令测试通过")
                return True
            else:
                print(f"❌ paddleocr命令测试失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ paddleocr命令测试超时")
            return False
        except FileNotFoundError:
            print("❌ paddleocr命令不存在")
            return False
        except Exception as e:
            print(f"❌ paddleocr命令测试异常: {e}")
            return False

    def get_device_info(self) -> str:
        """获取设备信息"""
        return f"{self.device} (via subprocess)"

    def process_file(self, file_path: str) -> Optional[List[List[str]]]:
        """
        处理文件并进行OCR识别

        Args:
            file_path: 文件路径

        Returns:
            OCR识别结果的列表格式
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 验证文件可读性和大小
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError(f"文件为空: {file_path}")
            print(f"📁 文件大小: {file_size} 字节")
        except OSError as e:
            raise ValueError(f"无法读取文件信息: {file_path}, 错误: {e}")

        try:
            print(f"🔍 开始OCR处理: {file_path}")

            # 确保文件路径使用绝对路径
            abs_file_path = os.path.abspath(file_path)
            print(f"📍 绝对路径: {abs_file_path}")

            # 构建paddleocr命令，使用绝对路径
            cmd = [
                self.ocr_command,
                "ocr",
                "-i", abs_file_path,
                "--device", self.device
            ]

            print(f"🚀 执行命令: {' '.join(cmd)}")

            # 执行OCR命令，合并stdout和stderr
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=os.path.dirname(abs_file_path) or "."
            )

            print(f"📊 命令返回码: {result.returncode}")
            print(f"📄 stdout长度: {len(result.stdout)} 字符")
            print(f"📄 stderr长度: {len(result.stderr)} 字符")

            # 首先检查stderr中是否有错误信息（无论返回码如何）
            if result.stderr:
                print(f"stderr内容预览: {result.stderr[:300]}...")

                # 检查是否有Python异常或错误（无论返回码如何）
                if "Traceback" in result.stderr:
                    print("❌ 检测到Python Traceback，这是一个错误")
                    return {"error": f"OCR处理失败: Python异常 - {result.stderr.split('Traceback')[-1][:200]}..."}
                elif "Exception:" in result.stderr:
                    print("❌ 检测到Exception，这是一个错误")
                    exception_msg = result.stderr.split('Exception:')[-1].strip()
                    return {"error": f"OCR处理失败: {exception_msg[:200]}"}
                elif "can't open/read file" in result.stderr:
                    return {"error": f"无法读取文件: {abs_file_path}，请检查文件是否存在且可读"}
                elif "Image read Error" in result.stderr:
                    return {"error": f"图像读取错误: {abs_file_path}，请检查文件格式是否正确"}
                elif "Error:" in result.stderr or "ERROR" in result.stderr:
                    print("❌ 检测到错误信息")
                    return {"error": f"OCR处理失败: {result.stderr[:200]}"}

            # 如果没有明显错误，但返回码非零，也是错误
            if result.returncode != 0:
                print(f"⚠️  命令返回非零状态码: {result.returncode}")
                return {"error": f"OCR命令执行失败，返回码: {result.returncode}"}

            # 合并stdout和stderr的输出
            combined_output = result.stdout + "\n" + result.stderr

            # 智能选择输出进行解析
            # 如果stderr包含调试信息但没有错误，可能包含结果
            if result.stderr and not any(error_keyword in result.stderr for error_keyword in ["Traceback", "Exception", "Error:", "ERROR"]):
                print("✅ stderr包含调试信息，尝试解析")
                output_to_parse = result.stderr.strip()
            else:
                print("✅ 使用stdout进行解析")
                output_to_parse = result.stdout.strip()

            if not output_to_parse:
                print("⚠️  主要输出为空，尝试解析合并输出")
                output_to_parse = combined_output.strip()

            print(f"📄 用于解析的输出长度: {len(output_to_parse)} 字符")

            if not output_to_parse:
                print("❌ 没有可解析的输出")
                return []

            # 最后一次检查：如果输出主要是错误信息，返回错误
            if self._is_mainly_error_output(output_to_parse):
                print("❌ 输出主要包含错误信息")
                return {"error": "OCR处理失败: 命令执行出现错误，请检查文件格式和系统环境"}

            # 显示输出的前500个字符用于调试
            print(f"🔍 输出预览: {output_to_parse[:500]}...")

            # 解析OCR结果
            parsed_result = self._parse_ocr_output(output_to_parse)
            print(f"✅ 解析完成，提取了 {len(parsed_result)} 页内容")

            return parsed_result

        except subprocess.TimeoutExpired:
            error_msg = f"OCR处理超时 (超过{self.timeout}秒)"
            print(f"❌ {error_msg}")
            return {"error": error_msg}

        except Exception as e:
            error_msg = f"OCR处理异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {"error": error_msg}

    def _parse_ocr_output(self, output: str) -> List[List[str]]:
        """
        解析paddleocr命令行输出

        Args:
            output: paddleocr命令的标准输出

        Returns:
            解析后的文本列表，格式为 [[page1_texts], [page2_texts], ...]
        """
        try:
            # 首先尝试查找'rec_texts'字段（从您的输出可以看到这是关键字段）
            rec_texts_match = re.search(r"'rec_texts':\s*\[(.*?)\]", output, re.DOTALL)
            if rec_texts_match:
                print("✅ 找到rec_texts字段")
                rec_texts_str = rec_texts_match.group(1)

                # 提取引号中的文本
                texts = re.findall(r"'([^']*)'", rec_texts_str)
                if texts:
                    print(f"✅ 从rec_texts提取到 {len(texts)} 个文本")
                    return [texts]  # 返回单页结果

            # 备用方法：查找字典格式的输出
            if "'res':" in output:
                print("✅ 检测到字典格式输出")
                # 尝试使用eval解析（注意：这在生产环境中不安全，但对于已知格式可以使用）
                try:
                    # 查找完整的字典结构
                    dict_match = re.search(r"(\{'res':.*?\})\s*$", output, re.DOTALL | re.MULTILINE)
                    if dict_match:
                        dict_str = dict_match.group(1)
                        # 替换numpy数组为空列表以便解析
                        dict_str = re.sub(r"array\([^)]+\)", "[]", dict_str)

                        # 尝试解析字典
                        result_dict = eval(dict_str)
                        if 'res' in result_dict and 'rec_texts' in result_dict['res']:
                            texts = result_dict['res']['rec_texts']
                            print(f"✅ 从字典解析到 {len(texts)} 个文本")
                            return [texts]
                except Exception as e:
                    print(f"⚠️  字典解析失败: {e}")

            # 传统方法：逐行解析
            lines = output.split('\n')
            text_results = []
            current_page_texts = []

            # 查找包含OCR结果的行
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 跳过调试信息、日志和错误信息
                if any(skip_word in line.lower() for skip_word in [
                    'loading', 'model', 'inference', 'time', 'total', 'download',
                    'cache', 'warning', 'info', 'debug', 'error', 'creating',
                    'using', 'official', 'processed', 'paddleocr', 'traceback',
                    'exception', 'init.cc', 'paddle/fluid', '/usr/local/lib/python',
                    'sys.exit', 'main()', '_execute', 'args.executor',
                    'import paddle', '__init__.py', '__bootstrap__',
                    'core.init_devices', 'at /paddle/'
                ]):
                    continue

                # 尝试解析JSON格式的结果
                if line.startswith('[') and line.endswith(']'):
                    try:
                        json_result = json.loads(line)
                        page_texts = self._extract_texts_from_json(json_result)
                        if page_texts:
                            text_results.append(page_texts)
                        continue
                    except json.JSONDecodeError:
                        pass

                # 尝试提取文本内容（处理各种可能的输出格式）
                text = self._extract_text_from_line(line)
                if text:
                    current_page_texts.append(text)

            # 如果有当前页面的文本，添加到结果中
            if current_page_texts:
                text_results.append(current_page_texts)

            # 如果没有找到任何文本，尝试更宽松的解析
            if not text_results:
                text_results = self._fallback_parse(output)

            return text_results if text_results else [[]]

        except Exception as e:
            print(f"⚠️  解析OCR输出时出错: {e}")
            # 返回原始输出作为备选
            return [[str(e)]]

    def _extract_texts_from_json(self, json_result: List) -> List[str]:
        """从JSON结果中提取文本"""
        texts = []
        try:
            for item in json_result:
                if isinstance(item, list) and len(item) >= 2:
                    # 标准格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], ['文本内容', 置信度]]
                    if isinstance(item[1], list) and len(item[1]) >= 1:
                        text = item[1][0]
                        if isinstance(text, str) and text.strip():
                            texts.append(text.strip())
        except Exception as e:
            print(f"⚠️  JSON解析错误: {e}")
        
        return texts

    def _extract_text_from_line(self, line: str) -> Optional[str]:
        """从单行输出中提取文本"""
        # 移除常见的前缀和后缀
        line = line.strip()
        
        # 跳过明显的非文本行
        if any(skip_pattern in line.lower() for skip_pattern in [
            'http', 'www', '.com', '.cn', 'model', 'loading', 'time:',
            'total time', 'inference time', 'download', 'cache'
        ]):
            return None
        
        # 尝试提取引号中的内容
        quote_match = re.search(r'["\']([^"\']+)["\']', line)
        if quote_match:
            return quote_match.group(1).strip()
        
        # 如果行看起来像是纯文本（包含中文或英文字母）
        if re.search(r'[\u4e00-\u9fff]|[a-zA-Z]', line):
            # 移除可能的坐标信息
            cleaned = re.sub(r'\[\[.*?\]\]', '', line)
            cleaned = re.sub(r'\[.*?\]', '', cleaned)
            cleaned = cleaned.strip()
            
            if cleaned and len(cleaned) > 1:
                return cleaned
        
        return None

    def _fallback_parse(self, output: str) -> List[List[str]]:
        """备用解析方法"""
        try:
            print("🔧 使用备用解析方法")

            # 方法1：查找所有引号中的中文文本
            chinese_texts = re.findall(r"'([^']*[\u4e00-\u9fff][^']*)'", output)
            if chinese_texts:
                print(f"✅ 备用方法1：找到 {len(chinese_texts)} 个中文文本")
                return [chinese_texts]

            # 方法2：查找所有引号中的文本（包括英文）
            all_quoted_texts = re.findall(r"'([^']+)'", output)
            if all_quoted_texts:
                # 过滤掉明显不是OCR结果的文本
                filtered_texts = []
                for text in all_quoted_texts:
                    if (len(text) > 1 and
                        not any(skip in text.lower() for skip in [
                            'http', 'www', '.com', '.cn', 'model', 'loading',
                            'time', 'download', 'cache', 'input_path', 'page_index'
                        ]) and
                        (re.search(r'[\u4e00-\u9fff]', text) or  # 包含中文
                         re.search(r'[a-zA-Z]{2,}', text))):     # 包含英文单词
                        filtered_texts.append(text)

                if filtered_texts:
                    print(f"✅ 备用方法2：找到 {len(filtered_texts)} 个文本")
                    return [filtered_texts]

            # 方法3：简单地将所有看起来像文本的行收集起来
            lines = output.split('\n')
            texts = []

            for line in lines:
                text = self._extract_text_from_line(line)
                if text:
                    texts.append(text)

            if texts:
                print(f"✅ 备用方法3：找到 {len(texts)} 个文本")
                return [texts]

            print("⚠️  所有备用方法都未找到文本")
            return []

        except Exception as e:
            print(f"⚠️  备用解析失败: {e}")
            return []

    def _extract_useful_content_from_stderr(self, stderr_content: str) -> str:
        """从stderr中提取有用的内容，过滤掉错误信息"""
        try:
            lines = stderr_content.split('\n')
            useful_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 跳过明显的错误和调试信息
                if any(skip_pattern in line for skip_pattern in [
                    'Traceback', 'Exception', 'Error:', 'ERROR', 'WARNING', 'WARN',
                    'init.cc', 'paddle/fluid', '/usr/local/lib/python',
                    'sys.exit', 'main()', '_execute', 'args.executor',
                    'import paddle', '__init__.py', '__bootstrap__',
                    'core.init_devices', 'at /paddle/'
                ]):
                    continue

                # 查找可能的OCR结果
                if any(useful_pattern in line for useful_pattern in [
                    "'rec_texts':", "{'res':", "'texts':"
                ]):
                    useful_lines.append(line)

            return '\n'.join(useful_lines)

        except Exception as e:
            print(f"⚠️  从stderr提取内容失败: {e}")
            return ""

    def _is_mainly_error_output(self, output: str) -> bool:
        """检查输出是否主要包含错误信息"""
        try:
            lines = output.split('\n')
            total_lines = len([line for line in lines if line.strip()])

            if total_lines == 0:
                return False

            error_lines = 0
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是错误相关的行
                if any(error_pattern in line for error_pattern in [
                    'Traceback', 'Exception', 'Error:', 'ERROR', 'WARN',
                    'init.cc', 'paddle/fluid', '/usr/local/lib/python',
                    'sys.exit', 'main()', '_execute', 'args.executor',
                    'import paddle', '__init__.py', '__bootstrap__',
                    'core.init_devices', 'at /paddle/', 'File "/'
                ]):
                    error_lines += 1

            # 如果超过70%的行都是错误信息，认为主要是错误输出
            error_ratio = error_lines / total_lines
            print(f"🔍 错误行比例: {error_ratio:.2f} ({error_lines}/{total_lines})")

            return error_ratio > 0.7

        except Exception as e:
            print(f"⚠️  检查错误输出失败: {e}")
            return False
