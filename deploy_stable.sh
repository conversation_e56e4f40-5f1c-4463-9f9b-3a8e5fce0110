#!/bin/bash

# PaddleOCR 稳定版本部署脚本
# 使用 PaddleOCR 2.7.3 + PaddlePaddle 2.5.2

echo "🚀 PaddleOCR 3.0.0版本部署"
echo "========================="

echo "📋 版本信息:"
echo "  PaddleOCR: 3.0.0"
echo "  PaddlePaddle: 3.0.0"
echo "  Python: 3.12"
echo "  NumPy: 1.26.4"
echo "  模式: 在线自动下载模型"
echo ""

# 检查Docker环境
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查网络连接
echo "🌐 检查网络连接..."
if curl -s --connect-timeout 5 https://www.baidu.com >/dev/null; then
    echo "✅ 网络连接正常"
else
    echo "⚠️ 网络连接可能有问题，但继续部署"
fi

# 检查必要文件
required_files=("app.py" "ocr_processor.py" "run.py" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 项目文件检查通过"

# 显示当前版本配置
echo "📦 当前依赖版本:"
grep -E "(paddlepaddle|paddleocr|numpy)" requirements.txt

# 询问是否继续
echo ""
echo "🚀 是否开始部署PaddleOCR 3.0.0版本？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消部署"
    exit 0
fi

# 停止旧容器
if docker ps | grep -q paddleocr-service; then
    echo "🛑 停止旧容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 备份旧镜像
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    echo "💾 备份旧镜像..."
    docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-unstable-backup 2>/dev/null || true
    echo "✅ 旧镜像已备份为: paddleocr-service:arm64-unstable-backup"
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 构建稳定版本镜像
echo ""
echo "🔨 构建稳定版本镜像..."
echo "使用 PaddleOCR 2.7.3 + PaddlePaddle 2.5.2"
echo "这可能需要 10-30 分钟，请耐心等待..."

start_time=$(date +%s)

if docker build -f Dockerfile.online -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ 稳定版本镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 显示镜像信息
image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "paddleocr-service" | grep "arm64-latest" | awk '{print $3}')
echo "📦 镜像大小: ${image_size}"

# 部署稳定版本服务
echo ""
echo "🚀 部署稳定版本服务..."
docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --cpus 2 \
    --shm-size 512m \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ 稳定版本服务部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
echo "注意: 首次启动时PaddleOCR 2.7.3会自动下载模型"
sleep 25

# 检查容器状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 显示启动日志
echo ""
echo "📋 服务启动日志:"
docker logs paddleocr-service --tail 15

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查超时"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 验证版本信息
echo ""
echo "🔍 验证版本信息..."
docker exec paddleocr-service python -c "
try:
    import paddle
    print(f'✅ PaddlePaddle版本: {paddle.__version__}')
    
    import paddleocr
    print('✅ PaddleOCR 2.7.3 导入成功')
    
    import numpy
    print(f'✅ NumPy版本: {numpy.__version__}')
except Exception as e:
    print(f'❌ 版本验证失败: {e}')
"

# 测试服务信息
echo ""
echo "ℹ️ 获取服务信息..."
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ 服务信息:')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
    else:
        print('❌ 服务信息获取失败')
except Exception as e:
    print(f'⚠️ 服务信息获取异常: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法获取服务信息，但服务可能仍在初始化中"
fi

echo ""
echo "🎉 稳定版本部署完成！"
echo "===================="
echo "✅ PaddleOCR 2.7.3 稳定版本已部署"
echo "✅ 使用单线程配置，避免ARM64环境下的冲突"
echo "✅ 模型将在首次使用时自动下载"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🧪 测试命令:"
echo "   # 快速测试"
echo "   python3 quick_test.py"
echo ""
echo "   # 完整测试"
echo "   python3 test_deployment.py"
echo ""
echo "   # 容器内测试"
echo "   docker exec -it paddleocr-service bash"
echo "   paddleocr --image_dir /path/to/image.png"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: docker logs paddleocr-service"
echo "   重启服务: docker restart paddleocr-service"
echo "   查看资源: docker stats paddleocr-service"
echo ""
echo "💾 备份信息:"
echo "   不稳定版本备份: paddleocr-service:arm64-unstable-backup"
echo "   删除备份: docker rmi paddleocr-service:arm64-unstable-backup"
echo ""
echo "✨ 稳定版本特点:"
echo "   - PaddleOCR 2.7.3: 经过验证的稳定版本"
echo "   - PaddlePaddle 2.5.2: 与ARM64兼容性好"
echo "   - 单线程配置: 避免OpenBlas多线程冲突"
echo "   - Python 3.9: 稳定的Python版本"
echo ""

echo "✅ 部署流程完成！"
