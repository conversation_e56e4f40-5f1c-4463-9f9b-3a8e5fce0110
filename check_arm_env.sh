#!/bin/bash

# ARM服务器环境检查脚本

echo "========================================="
echo "ARM服务器环境检查"
echo "========================================="
echo

# 检查系统架构
echo "1. 系统架构检查:"
echo "   架构: $(uname -m)"
echo "   内核: $(uname -r)"
echo "   系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo

# 检查Docker状态
echo "2. Docker环境检查:"
if command -v docker &> /dev/null; then
    echo "   ✓ Docker已安装"
    echo "   版本: $(docker --version)"
    
    if docker info &> /dev/null; then
        echo "   ✓ Docker服务运行正常"
        echo "   存储驱动: $(docker info --format '{{.Driver}}')"
        echo "   可用空间: $(df -h /var/lib/docker | tail -1 | awk '{print $4}')"
    else
        echo "   ❌ Docker服务未运行"
        echo "   请执行: sudo systemctl start docker"
    fi
else
    echo "   ❌ Docker未安装"
fi
echo

# 检查昇腾设备
echo "3. 昇腾NPU设备检查:"
if ls /dev/davinci* &> /dev/null; then
    echo "   ✓ 检测到昇腾设备:"
    ls -la /dev/davinci* | while read line; do
        echo "     $line"
    done
    
    # 检查昇腾软件栈
    if [ -d "/usr/local/Ascend" ]; then
        echo "   ✓ 昇腾软件栈已安装: /usr/local/Ascend"
        
        # 检查npu-smi工具
        if [ -f "/usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi" ]; then
            echo "   ✓ npu-smi工具可用"
            echo "   NPU设备信息:"
            /usr/local/Ascend/ascend-toolkit/latest/bin/npu-smi info 2>/dev/null || echo "     无法获取NPU信息"
        else
            echo "   ⚠️  npu-smi工具不可用"
        fi
    else
        echo "   ⚠️  昇腾软件栈未安装"
    fi
else
    echo "   ⚠️  未检测到昇腾设备"
fi
echo

# 检查网络连接
echo "4. 网络连接检查:"
echo "   测试基础网络连接..."

# 测试Docker Hub连接
if curl -s --connect-timeout 5 https://registry-1.docker.io/v2/ &> /dev/null; then
    echo "   ✓ Docker Hub连接正常"
else
    echo "   ❌ Docker Hub连接失败"
fi

# 测试阿里云镜像连接
if curl -s --connect-timeout 5 https://mirrors.aliyun.com &> /dev/null; then
    echo "   ✓ 阿里云镜像连接正常"
else
    echo "   ❌ 阿里云镜像连接失败"
fi

# 测试PaddlePaddle官方源连接
if curl -s --connect-timeout 5 https://www.paddlepaddle.org.cn &> /dev/null; then
    echo "   ✓ PaddlePaddle官方源连接正常"
else
    echo "   ❌ PaddlePaddle官方源连接失败"
fi
echo

# 检查必要文件
echo "5. 项目文件检查:"
REQUIRED_FILES=("Dockerfile.ascend" "requirements.ascend.txt" "app.py" "ocr_processor.py" "run.py" ".env")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✓ $file"
    else
        echo "   ❌ $file (缺失)"
    fi
done
echo

# 检查CANN工具包
echo "6. CANN工具包检查:"
CANN_FILE="Ascend-cann-toolkit_8.1.RC1_linux-aarch64.run"
if [ -f "$CANN_FILE" ]; then
    echo "   ✓ $CANN_FILE ($(du -h $CANN_FILE | cut -f1))"
else
    echo "   ⚠️  $CANN_FILE (未找到，将跳过CANN安装)"
fi
echo

# 检查磁盘空间
echo "7. 磁盘空间检查:"
echo "   当前目录: $(df -h . | tail -1 | awk '{print $4}') 可用"
echo "   /tmp目录: $(df -h /tmp | tail -1 | awk '{print $4}') 可用"
echo "   Docker目录: $(df -h /var/lib/docker 2>/dev/null | tail -1 | awk '{print $4}' || echo '无法检查') 可用"
echo

# 检查内存
echo "8. 内存检查:"
echo "   总内存: $(free -h | grep Mem | awk '{print $2}')"
echo "   可用内存: $(free -h | grep Mem | awk '{print $7}')"
echo

echo "========================================="
echo "环境检查完成"
echo "========================================="
echo
echo "建议的构建命令:"
echo "docker build -f Dockerfile.ascend -t paddleocr-arm64-server:latest --no-cache --progress=plain ."
echo
echo "注意: 在ARM服务器上不需要 --platform linux/arm64 参数"
