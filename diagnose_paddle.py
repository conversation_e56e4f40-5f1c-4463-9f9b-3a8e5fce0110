#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PaddleOCR环境诊断脚本
用于检查paddle模块安装和导入问题
"""

import sys
import os
import subprocess

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def check_python_version():
    """检查Python版本"""
    print("=" * 50)
    print("Python版本检查")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()

def check_pip_packages():
    """检查已安装的包"""
    print("=" * 50)
    print("已安装的paddle相关包")
    print("=" * 50)
    
    # 检查paddle相关包
    code, stdout, stderr = run_command("pip list | grep -i paddle")
    if code == 0 and stdout.strip():
        print("已安装的paddle相关包:")
        print(stdout)
    else:
        print("❌ 未找到任何paddle相关包")
        print(f"错误信息: {stderr}")
    print()

def check_paddle_import():
    """检查paddle模块导入"""
    print("=" * 50)
    print("Paddle模块导入测试")
    print("=" * 50)
    
    try:
        import paddle
        print("✅ paddle模块导入成功")
        print(f"Paddle版本: {paddle.__version__}")
        print(f"Paddle安装路径: {paddle.__file__}")
        
        # 检查设备支持
        print(f"CUDA支持: {paddle.device.is_compiled_with_cuda()}")
        if paddle.device.is_compiled_with_cuda():
            print(f"GPU数量: {paddle.device.cuda.device_count()}")
        
        return True
    except ImportError as e:
        print(f"❌ paddle模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ paddle模块检查异常: {e}")
        return False

def check_paddleocr_import():
    """检查paddleocr模块导入"""
    print("=" * 50)
    print("PaddleOCR模块导入测试")
    print("=" * 50)
    
    try:
        import paddleocr
        print("✅ paddleocr模块导入成功")
        print(f"PaddleOCR安装路径: {paddleocr.__file__}")
        
        # 尝试创建PaddleOCR实例
        try:
            ocr = paddleocr.PaddleOCR(use_angle_cls=False, lang="ch")
            print("✅ PaddleOCR实例创建成功")
            return True
        except Exception as e:
            print(f"⚠️  PaddleOCR实例创建失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ paddleocr模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ paddleocr模块检查异常: {e}")
        return False

def check_system_info():
    """检查系统信息"""
    print("=" * 50)
    print("系统信息")
    print("=" * 50)
    
    # 检查操作系统
    import platform
    print(f"操作系统: {platform.system()}")
    print(f"系统版本: {platform.release()}")
    print(f"架构: {platform.machine()}")
    
    # 检查环境变量
    print("\n重要环境变量:")
    env_vars = ['LD_LIBRARY_PATH', 'CUDA_VISIBLE_DEVICES', 'FLAGS_selected_npus']
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"  {var}: {value}")
    
    # 检查昇腾设备
    print("\n昇腾设备检查:")
    ascend_devices = ['/dev/davinci0', '/dev/davinci1', '/dev/davinci_manager']
    for device in ascend_devices:
        if os.path.exists(device):
            print(f"  ✅ {device} 存在")
        else:
            print(f"  ❌ {device} 不存在")
    
    print()

def main():
    """主函数"""
    print("PaddleOCR环境诊断开始...")
    print()
    
    # 执行各项检查
    check_python_version()
    check_system_info()
    check_pip_packages()
    
    paddle_ok = check_paddle_import()
    paddleocr_ok = check_paddleocr_import()
    
    # 总结
    print("=" * 50)
    print("诊断总结")
    print("=" * 50)
    
    if paddle_ok and paddleocr_ok:
        print("✅ 所有检查通过，PaddleOCR环境正常")
    else:
        print("❌ 发现问题:")
        if not paddle_ok:
            print("  - paddle模块无法正常导入")
        if not paddleocr_ok:
            print("  - paddleocr模块无法正常导入")
        
        print("\n建议解决方案:")
        print("1. 重新安装paddle:")
        print("   pip uninstall paddle paddlepaddle paddlepaddle-gpu paddle-custom-npu")
        print("   pip install paddlepaddle-gpu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cu126/")
        print("2. 重新安装paddleocr:")
        print("   pip install paddleocr==3.0.0")
        print("3. 检查系统依赖是否完整")

if __name__ == "__main__":
    main()
