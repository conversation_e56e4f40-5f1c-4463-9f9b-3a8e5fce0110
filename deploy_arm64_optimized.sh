#!/bin/bash

# ARM64优化版本部署脚本
# 基于社区反馈的ARM64段错误解决方案

echo "🔧 PaddleOCR ARM64优化版本部署"
echo "============================="

echo "📋 优化说明:"
echo "  基于社区反馈的ARM64段错误解决方案"
echo "  ✅ 使用mobile模型替代server模型"
echo "  ✅ 限制图片尺寸到640px避免段错误"
echo "  ✅ 单线程CPU配置"
echo "  ✅ 禁用MKL-DNN优化"
echo ""

# 检查系统架构
arch=$(uname -m)
if [[ "$arch" != "aarch64" && "$arch" != "arm64" ]]; then
    echo "⚠️ 当前系统架构为 $arch，此优化主要针对ARM64架构"
    echo "是否继续部署？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "用户取消部署"
        exit 0
    fi
else
    echo "✅ 检测到ARM64架构: $arch"
fi

# 检查Docker环境
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查网络连接
echo "🌐 检查网络连接..."
if curl -s --connect-timeout 5 https://www.baidu.com >/dev/null; then
    echo "✅ 网络连接正常"
else
    echo "⚠️ 网络连接可能有问题，但继续部署"
fi

# 检查必要文件
required_files=("app.py" "ocr_processor.py" "run.py" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 项目文件检查通过"

# 显示优化配置
echo "🔧 ARM64优化配置:"
echo "   模型类型: mobile (PP-OCRv4_mobile_det/rec)"
echo "   图片尺寸限制: 640px"
echo "   CPU线程: 1"
echo "   MKL-DNN: 禁用"
echo "   内存限制: 4GB"
echo ""

# 询问是否继续
echo "🚀 是否开始部署ARM64优化版本？(y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "用户取消部署"
    exit 0
fi

# 停止旧容器
if docker ps | grep -q paddleocr-service; then
    echo "🛑 停止旧容器..."
    docker stop paddleocr-service
    docker rm paddleocr-service
fi

# 备份旧镜像
if docker images | grep -q "paddleocr-service.*arm64-latest"; then
    echo "💾 备份旧镜像..."
    docker tag paddleocr-service:arm64-latest paddleocr-service:arm64-unoptimized-backup 2>/dev/null || true
    echo "✅ 旧镜像已备份"
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi paddleocr-service:arm64-latest 2>/dev/null || true

# 构建ARM64优化版本镜像
echo ""
echo "🔨 构建ARM64优化版本镜像..."
echo "应用社区反馈的段错误解决方案"
echo "这可能需要 10-30 分钟，请耐心等待..."

start_time=$(date +%s)

if docker build -f Dockerfile.online -t paddleocr-service:arm64-latest .; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✅ ARM64优化版本镜像构建成功，耗时: ${duration}秒"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 显示镜像信息
image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "paddleocr-service" | grep "arm64-latest" | awk '{print $3}')
echo "📦 镜像大小: ${image_size}"

# 部署ARM64优化版本服务
echo ""
echo "🚀 部署ARM64优化版本服务..."
echo "使用社区推荐的稳定配置"

docker run -d \
    --name paddleocr-service \
    -p 9527:9527 \
    --restart unless-stopped \
    --memory 4g \
    --memory-swap 4g \
    --cpus 1 \
    --shm-size 512m \
    -e OMP_NUM_THREADS=1 \
    -e MKL_NUM_THREADS=1 \
    -e OPENBLAS_NUM_THREADS=1 \
    -e PADDLE_DISABLE_CUSTOM_DEVICE=1 \
    -e GLOG_v=0 \
    paddleocr-service:arm64-latest

if [ $? -eq 0 ]; then
    echo "✅ ARM64优化版本服务部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
echo "注意: 首次启动时会下载mobile模型，比server模型更快"
sleep 25

# 检查容器状态
if docker ps | grep -q paddleocr-service; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器未正常运行"
    echo "容器日志:"
    docker logs paddleocr-service --tail 30
    exit 1
fi

# 显示启动日志
echo ""
echo "📋 服务启动日志:"
docker logs paddleocr-service --tail 15

# 健康检查
echo ""
echo "🏥 执行健康检查..."
for i in {1..15}; do
    if curl -f "http://localhost:9527/health" >/dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 等待服务就绪... ($i/15)"
        sleep 10
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 健康检查超时"
        echo "容器日志:"
        docker logs paddleocr-service --tail 30
        exit 1
    fi
done

# 验证优化配置
echo ""
echo "🔍 验证ARM64优化配置..."
docker exec paddleocr-service python -c "
try:
    import paddle
    print(f'✅ PaddlePaddle版本: {paddle.__version__}')
    
    import paddleocr
    print('✅ PaddleOCR 3.0.0 导入成功')
    
    import numpy
    print(f'✅ NumPy版本: {numpy.__version__}')
    
    print('✅ ARM64优化配置已应用')
except Exception as e:
    print(f'❌ 验证失败: {e}')
"

# 测试服务信息
echo ""
echo "ℹ️ 获取服务信息..."
if python3 -c "
import requests
import json
try:
    response = requests.get('http://localhost:9527/', timeout=10)
    if response.status_code == 200:
        info = response.json()
        print('✅ 服务信息:')
        print(f'   服务: {info.get(\"service\", \"N/A\")}')
        print(f'   版本: {info.get(\"version\", \"N/A\")}')
    else:
        print('❌ 服务信息获取失败')
except Exception as e:
    print(f'⚠️ 服务信息获取异常: {e}')
" 2>/dev/null; then
    echo ""
else
    echo "⚠️ 无法获取服务信息，但服务可能仍在初始化中"
fi

# 段错误测试
echo ""
echo "🧪 执行段错误预防测试..."
echo "创建测试图片..."

python3 -c "
from PIL import Image, ImageDraw, ImageFont
import os

# 创建一个640x480的测试图片（在限制范围内）
img = Image.new('RGB', (640, 480), color='white')
draw = ImageDraw.Draw(img)

try:
    font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', 24)
except:
    font = ImageFont.load_default()

draw.text((50, 50), 'ARM64优化测试', fill='black', font=font)
draw.text((50, 100), 'Mobile Model Test', fill='black', font=font)
draw.text((50, 150), '图片尺寸: 640x480', fill='black', font=font)
draw.text((50, 200), '应该不会出现段错误', fill='black', font=font)

img.save('test_arm64_optimized.png')
print('✅ 测试图片已创建: test_arm64_optimized.png')
" 2>/dev/null || echo "⚠️ 无法创建测试图片"

if [ -f "test_arm64_optimized.png" ]; then
    echo "🔍 测试OCR识别（段错误预防）..."
    response=$(curl -s -X POST -F "file=@test_arm64_optimized.png" http://localhost:9527/ocr)
    
    if echo "$response" | grep -q "texts"; then
        echo "✅ ARM64优化版本OCR测试成功，无段错误！"
        echo "识别结果: $response"
    else
        echo "❌ OCR测试失败"
        echo "响应: $response"
        echo "容器日志:"
        docker logs paddleocr-service --tail 20
    fi
    
    # 清理测试文件
    rm -f test_arm64_optimized.png
else
    echo "⚠️ 跳过OCR测试（无测试图片）"
fi

echo ""
echo "🎉 ARM64优化版本部署完成！"
echo "============================"
echo "✅ 基于社区反馈的ARM64段错误解决方案已应用"
echo "✅ 使用mobile模型，兼容性更好"
echo "✅ 图片尺寸限制640px，避免段错误"
echo ""
echo "📋 服务信息:"
echo "   容器名称: paddleocr-service"
echo "   服务端口: 9527"
echo "   健康检查: http://localhost:9527/health"
echo "   OCR接口: http://localhost:9527/ocr"
echo ""
echo "🔧 优化特性:"
echo "   - Mobile模型: 更好的ARM64兼容性"
echo "   - 尺寸限制: 640px避免大图片段错误"
echo "   - 单线程: 避免多线程冲突"
echo "   - 内存优化: 4GB限制，避免过度使用"
echo ""
echo "🧪 测试命令:"
echo "   python3 quick_test.py"
echo "   python3 test_deployment.py"
echo ""
echo "⚠️ 重要提醒:"
echo "   - 如果仍有段错误，请检查输入图片尺寸"
echo "   - 建议图片尺寸不超过640x640"
echo "   - Mobile模型精度略低于Server模型，但稳定性更好"
echo ""

echo "✅ 部署流程完成！"
