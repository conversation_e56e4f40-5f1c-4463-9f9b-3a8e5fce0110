# ARM64本地构建专用Dockerfile
# 针对ARM64服务器本地构建优化

FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PADDLE_DISABLE_CUSTOM_DEVICE=1

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgfortran5 \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件并安装Python依赖
COPY requirements.txt .

# 设置pip源（使用国内镜像加速）
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple && \
    pip config set global.trusted-host mirrors.tuna.tsinghua.edu.cn

# 升级pip和安装基础工具
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 分步安装依赖，提高构建效率
RUN pip install --no-cache-dir \
    flask==3.0.0 \
    werkzeug==3.0.1 \
    "numpy==1.26.4" \
    "Pillow>=10.1.0" \
    python-dotenv==1.0.0

# 安装PDF处理依赖
RUN pip install --no-cache-dir "PyMuPDF>=1.23.0"

# 安装PaddlePaddle（ARM64版本）
RUN pip install --no-cache-dir paddlepaddle==3.1.0

# 安装PaddleOCR
RUN pip install --no-cache-dir paddleocr==3.1.0

# 复制模型文件并预加载到PaddleOCR 3.1.0期望的路径
COPY paddlex_models.tar.gz /tmp/
RUN mkdir -p /root/.paddlex && \
    cd /tmp && \
    tar -xzf paddlex_models.tar.gz && \
    # PaddleOCR 3.1.0 期望模型在 /root/.paddlex/ 路径下
    cp -r .paddlex/* /root/.paddlex/ && \
    rm -rf /tmp/paddlex_models.tar.gz /tmp/.paddlex && \
    echo "✅ PaddleOCR模型预加载完成" && \
    echo "📦 .paddlex 目录结构:" && \
    find /root/.paddlex -type d | head -20 && \
    echo "📦 official_models 内容:" && \
    ls -la /root/.paddlex/official_models/ 2>/dev/null || echo "official_models目录不存在" && \
    echo "📦 模型目录大小:" && \
    du -sh /root/.paddlex/

# 复制应用代码
COPY app.py .
COPY ocr_processor.py .
COPY load_env.py .
COPY run.py .
COPY .env* ./

# 创建日志目录
RUN mkdir -p /app/logs

# 验证安装
RUN python -c "import paddle; print('PaddlePaddle version:', paddle.__version__)" && \
    python -c "import paddleocr; print('PaddleOCR imported successfully')" && \
    pip list | grep paddle

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:9527/health || exit 1

# 暴露端口
EXPOSE 9527

# 启动应用
CMD ["python", "run.py"]
