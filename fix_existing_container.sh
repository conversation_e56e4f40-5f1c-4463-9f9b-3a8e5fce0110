#!/bin/bash

# 修复现有离线容器的脚本
# 在已运行的容器中安装缺失的PaddleOCR依赖

set -e

CONTAINER_NAME="paddleocr-offline"

echo "========================================="
echo "修复现有离线容器"
echo "========================================="
echo

# 检查容器是否存在
if ! docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "❌ 错误: 容器 $CONTAINER_NAME 不存在"
    exit 1
fi

# 检查容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "🔄 启动容器..."
    docker start "$CONTAINER_NAME"
    sleep 5
fi

echo "🔧 在容器中安装PaddleOCR依赖..."

# 在容器中执行安装命令
docker exec -it "$CONTAINER_NAME" bash -c "
echo '开始安装PaddleOCR依赖...'

# 1. 安装PaddlePaddle和paddle-custom-npu
echo '1. 安装PaddlePaddle...'
pip install paddlepaddle -i https://www.paddlepaddle.org.cn/packages/nightly/cpu

echo '2. 安装paddle-custom-npu...'
pip install paddle-custom-npu -i https://www.paddlepaddle.org.cn/packages/nightly/npu

# 3. 安装PaddleOCR
echo '3. 安装PaddleOCR 3.1.0...'
pip install paddleocr==3.1.0

# 4. 更新numpy
echo '4. 更新numpy版本...'
pip uninstall -y numpy
pip install numpy==1.26.4

# 5. 验证安装
echo '5. 验证安装...'
which paddleocr
paddleocr --help

echo '✅ 所有依赖安装完成!'
"

if [ $? -eq 0 ]; then
    echo
    echo "✅ 容器修复成功!"
    echo
    echo "🔄 重启容器以应用更改..."
    docker restart "$CONTAINER_NAME"
    
    echo "⏳ 等待服务启动..."
    sleep 10
    
    echo "🧪 测试服务..."
    if curl -s http://localhost:9527/health >/dev/null 2>&1; then
        echo "✅ 服务健康检查通过"
        echo
        echo "🎉 修复完成! 现在可以正常使用OCR服务了"
        echo
        echo "测试命令:"
        echo "  curl http://localhost:9527/health"
        echo "  curl -X POST -F 'file=@test_image.jpg' http://localhost:9527/ocr"
    else
        echo "⚠️  服务可能还在启动中，请稍后再试"
        echo "查看日志: docker logs $CONTAINER_NAME"
    fi
else
    echo "❌ 容器修复失败"
    echo "查看容器日志: docker logs $CONTAINER_NAME"
    exit 1
fi
