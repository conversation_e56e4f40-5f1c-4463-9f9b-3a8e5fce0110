#!/bin/bash

# PaddleOCR 昇腾NPU快速部署脚本
# 一键完成从环境配置到服务启动的全过程

set -e

echo "========================================="
echo "PaddleOCR 昇腾NPU 快速部署脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 此脚本只能在ARM64 (aarch64) 系统上运行"
    exit 1
fi

# 检查是否为root用户或有sudo权限
if [ "$EUID" -ne 0 ]; then
    if ! sudo -n true 2>/dev/null; then
        echo "❌ 错误: 需要root权限或sudo权限"
        echo "请使用: sudo $0 或以root用户运行"
        exit 1
    fi
    SUDO="sudo"
else
    SUDO=""
fi

echo "✓ 权限检查通过"
echo

# 步骤0: 检测昇腾设备
echo "步骤0: 检测昇腾NPU设备..."
if [ -f "check_ascend_devices.sh" ]; then
    chmod +x check_ascend_devices.sh
    echo "运行设备检测脚本..."
    ./check_ascend_devices.sh
    echo
    read -p "设备检测完成，是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 1
    fi
else
    echo "⚠️  警告: check_ascend_devices.sh 不存在，跳过设备检测"
fi
echo

# 步骤1: 环境配置
echo "步骤1: 配置服务器环境..."
if [ -f "server_setup.sh" ]; then
    chmod +x server_setup.sh
    $SUDO ./server_setup.sh
else
    echo "⚠️  警告: server_setup.sh 不存在，跳过环境配置"
    echo "请确保已手动配置Docker环境"
fi
echo

# 步骤2: 检查Docker
echo "步骤2: 检查Docker环境..."
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先运行 server_setup.sh"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo "启动Docker服务..."
    $SUDO systemctl start docker
fi

echo "✓ Docker环境正常"
echo

# 步骤3: 检查镜像
echo "步骤3: 检查Docker镜像..."
IMAGE_FOUND=false

# 检查是否有现成的镜像
if docker images | grep -q "paddleocr-arm64-server\|paddleocr-ascend"; then
    echo "✓ 发现现有镜像"
    IMAGE_FOUND=true
fi

# 检查是否有tar文件可以加载
if [ ! "$IMAGE_FOUND" = true ]; then
    TAR_FILES=(
        "paddleocr-ascend-arm64.tar"
        "paddleocr-arm64-server.tar"
        "*.tar"
    )
    
    for pattern in "${TAR_FILES[@]}"; do
        for file in $pattern; do
            if [ -f "$file" ] && [[ "$file" == *"paddleocr"* ]]; then
                echo "发现镜像文件: $file"
                echo "加载Docker镜像..."
                docker load -i "$file"
                IMAGE_FOUND=true
                break 2
            fi
        done
    done
fi

# 如果没有镜像，尝试构建
if [ ! "$IMAGE_FOUND" = true ]; then
    echo "未发现现有镜像，开始构建..."
    if [ -f "build_arm64_server.sh" ]; then
        chmod +x build_arm64_server.sh
        ./build_arm64_server.sh
    elif [ -f "Dockerfile.ascend" ]; then
        echo "使用Dockerfile.ascend构建镜像..."
        docker build -f Dockerfile.ascend -t paddleocr-arm64-server:latest .
    else
        echo "❌ 错误: 无法找到构建文件或镜像"
        echo "请确保以下文件之一存在:"
        echo "  - paddleocr-ascend-arm64.tar (镜像文件)"
        echo "  - build_arm64_server.sh (构建脚本)"
        echo "  - Dockerfile.ascend (Dockerfile)"
        exit 1
    fi
fi

echo "✓ Docker镜像准备完成"
echo

# 步骤4: 启动服务
echo "步骤4: 启动PaddleOCR服务..."
if [ -f "run_container.sh" ]; then
    chmod +x run_container.sh
    ./run_container.sh
else
    echo "⚠️  警告: run_container.sh 不存在，使用默认启动方式"
    
    # 检查昇腾设备并启动
    CONTAINER_NAME="paddleocr-arm64-server"
    IMAGE_NAME="paddleocr-arm64-server:latest"
    
    # 如果没有arm64-server镜像，使用ascend镜像
    if ! docker images | grep -q "paddleocr-arm64-server"; then
        IMAGE_NAME="paddleocr-ascend:latest"
    fi
    
    # 停止旧容器
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    
    if [ -e "/dev/davinci0" ]; then
        echo "检测到昇腾NPU设备，启用NPU模式..."
        docker run --name $CONTAINER_NAME -p 9527:9527 \
            --device=/dev/davinci0:/dev/davinci0 \
            --device=/dev/davinci_manager:/dev/davinci_manager \
            --device=/dev/devmm_svm:/dev/devmm_svm \
            --device=/dev/hisi_hdc:/dev/hisi_hdc \
            -v /usr/local/Ascend:/usr/local/Ascend:ro \
            --restart=unless-stopped \
            -d $IMAGE_NAME
    else
        echo "未检测到昇腾设备，使用CPU模式..."
        docker run --name $CONTAINER_NAME -p 9527:9527 \
            --restart=unless-stopped \
            -d $IMAGE_NAME
    fi
fi

echo

# 步骤5: 验证部署
echo "步骤5: 验证部署..."
sleep 10

# 检查容器状态
if docker ps | grep -q "paddleocr"; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败"
    docker ps -a | grep paddleocr
    echo "查看日志: docker logs paddleocr-arm64-server"
    exit 1
fi

# 健康检查
echo "进行健康检查..."
for i in {1..6}; do
    if curl -f http://localhost:9527/health >/dev/null 2>&1; then
        echo "✅ 服务健康检查通过"
        break
    else
        if [ $i -eq 6 ]; then
            echo "❌ 服务健康检查失败"
            echo "请查看日志: docker logs paddleocr-arm64-server"
            exit 1
        else
            echo "等待服务启动... ($i/6)"
            sleep 10
        fi
    fi
done

echo

# 显示部署结果
echo "========================================="
echo "✅ PaddleOCR服务部署成功!"
echo "========================================="
echo
echo "服务信息:"
echo "  - 服务地址: http://localhost:9527"
echo "  - 健康检查: http://localhost:9527/health"
echo "  - API接口: http://localhost:9527/ocr"
echo
echo "容器信息:"
docker ps | grep paddleocr
echo
echo "测试命令:"
echo "  curl http://localhost:9527/health"
echo "  curl -X POST -F \"file=@test_image.png\" http://localhost:9527/ocr"
echo
echo "管理命令:"
echo "  查看日志: docker logs paddleocr-arm64-server"
echo "  进入容器: docker exec -it paddleocr-arm64-server bash"
echo "  停止服务: docker stop paddleocr-arm64-server"
echo "  重启服务: docker restart paddleocr-arm64-server"
echo
echo "如果使用昇腾NPU，可以通过以下命令查看NPU状态:"
echo "  npu-smi info"
echo

# 检查昇腾设备状态
if [ -e "/dev/davinci0" ] && command -v npu-smi &> /dev/null; then
    echo "昇腾NPU设备状态:"
    npu-smi info || echo "无法获取NPU状态信息"
fi

echo "部署完成! 🎉"
