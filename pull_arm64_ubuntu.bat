@echo off
chcp 65001 >nul
echo ========================================
echo Pulling ARM64 Ubuntu Base Image
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running, please start Docker Desktop
    pause
    exit /b 1
)

echo Current Ubuntu images on your system:
docker images ubuntu
echo.

echo Checking current ubuntu:22.04 architecture:
docker inspect ubuntu:22.04 --format="Architecture: {{.Architecture}}" 2>nul
echo.

echo Attempting to pull ARM64 version of Ubuntu 22.04...
echo This may take several minutes depending on your network speed...
echo.

REM Try multiple mirror sources for ARM64 Ubuntu
echo Method 1: Trying Docker Hub directly...
docker pull --platform linux/arm64 ubuntu:22.04

if %errorlevel% eq 0 (
    echo SUCCESS: ARM64 Ubuntu image pulled from Docker Hub
    goto :verify
)

echo.
echo Method 1 failed. Trying Method 2: Tencent Cloud mirror...
docker pull --platform linux/arm64 ccr.ccs.tencentyun.com/library/ubuntu:22.04
if %errorlevel% eq 0 (
    echo SUCCESS: ARM64 Ubuntu image pulled from Tencent Cloud
    echo Tagging as ubuntu:22.04...
    docker tag ccr.ccs.tencentyun.com/library/ubuntu:22.04 ubuntu:22.04-arm64
    goto :verify
)

echo.
echo Method 2 failed. Trying Method 3: Alibaba Cloud mirror...
docker pull --platform linux/arm64 registry.cn-hangzhou.aliyuncs.com/library/ubuntu:22.04
if %errorlevel% eq 0 (
    echo SUCCESS: ARM64 Ubuntu image pulled from Alibaba Cloud
    echo Tagging as ubuntu:22.04-arm64...
    docker tag registry.cn-hangzhou.aliyuncs.com/library/ubuntu:22.04 ubuntu:22.04-arm64
    goto :verify
)

echo.
echo All methods failed. This might be due to:
echo 1. Network connectivity issues
echo 2. Docker registry access restrictions
echo 3. Firewall blocking Docker Hub access
echo.
echo Possible solutions:
echo 1. Configure Docker registry mirrors in Docker Desktop settings
echo 2. Use VPN or proxy
echo 3. Contact your network administrator
echo.
pause
exit /b 1

:verify
echo.
echo ========================================
echo Verification
echo ========================================
echo.
echo Current Ubuntu images:
docker images ubuntu
echo.
echo Checking ARM64 image architecture:
docker inspect ubuntu:22.04 --format="Architecture: {{.Architecture}}" 2>nul
echo.
echo If you see 'arm64' above, you can now run:
echo build_ascend_arm64_local.bat
echo.
pause
