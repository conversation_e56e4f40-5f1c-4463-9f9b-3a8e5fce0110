# ARM64架构专用Dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY app.py .
COPY ocr_processor.py .
COPY load_env.py .
COPY run.py .
COPY .env .
COPY paddlex_models.tar.gz .

# 设置pip源为清华大学镜像
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

# 升级pip和安装必要的工具
RUN pip install --upgrade pip setuptools wheel

# 先安装基本依赖
RUN pip install --no-cache-dir flask==3.0.0 werkzeug==3.0.1 "numpy==1.26.4" "Pillow>=10.1.0" python-dotenv==1.0.0

# 安装PyMuPDF
RUN pip install --no-cache-dir "PyMuPDF>=1.23.0"

# 安装paddlepaddle (ARM64版本)
RUN pip install --no-cache-dir paddlepaddle==3.1.0

# 安装paddleocr
RUN pip install --no-cache-dir paddleocr==3.1.0

# 预加载PaddleOCR模型
RUN mkdir -p /root/.paddleocr && \
    tar -xzf paddlex_models.tar.gz && \
    # 将模型文件从 .paddlex/official_models/ 移动到正确位置
    if [ -d ".paddlex/official_models" ]; then \
        cp -r .paddlex/official_models/* /root/.paddleocr/ && \
        echo "✅ 从 .paddlex/official_models/ 复制模型文件"; \
    else \
        cp -r .paddlex/* /root/.paddleocr/ && \
        echo "✅ 从 .paddlex/ 复制所有文件"; \
    fi && \
    rm -rf paddlex_models.tar.gz .paddlex && \
    echo "✅ PaddleOCR模型预加载完成" && \
    ls -la /root/.paddleocr/ && \
    ls -la /root/.paddleocr/

# 设置环境变量，指定使用预加载的模型
ENV PADDLEOCR_HOME=/root/.paddleocr
ENV PADDLE_DISABLE_CUSTOM_DEVICE=1

# 确保安装了正确版本的依赖
RUN pip list | grep paddle

# 暴露端口
EXPOSE 9527

# 启动应用
CMD ["python", "run.py"]
