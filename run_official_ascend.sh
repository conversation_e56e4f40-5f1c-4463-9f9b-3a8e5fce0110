#!/bin/bash

# 快速启动脚本 - 使用官方推荐的启动方式
# 基于官方示例命令修改

set -e

echo "========================================="
echo "PaddleOCR 官方昇腾镜像快速启动"
echo "========================================="
echo

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    exit 1
fi

# 设置容器信息
IMAGE_NAME="paddleocr-official-ascend:latest"
CONTAINER_NAME="paddleocr-official-ascend"

# 检查镜像是否存在
if ! docker images | grep -q "paddleocr-official-ascend"; then
    echo "❌ 错误: 未找到镜像 $IMAGE_NAME"
    echo "请先构建镜像：./build_official_ascend.sh"
    exit 1
fi

# 停止并删除现有容器（如果存在）
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🔄 停止并删除现有容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 检查昇腾环境
echo "🔍 检查昇腾环境..."
NPU_AVAILABLE=false

if [ -d "/usr/local/Ascend/driver" ] && [ -f "/usr/local/bin/npu-smi" ]; then
    echo "✅ 检测到昇腾环境，将启用NPU支持"
    NPU_AVAILABLE=true
else
    echo "⚠️  未检测到完整昇腾环境，将以CPU模式运行"
fi

# 构建启动命令
if [ "$NPU_AVAILABLE" = true ]; then
    echo "🚀 使用官方推荐方式启动容器（NPU模式）..."

    # 检查并挂载更多昇腾环境路径
    ASCEND_MOUNTS=""

    # 挂载昇腾驱动目录
    if [ -d "/usr/local/Ascend/driver" ]; then
        ASCEND_MOUNTS="$ASCEND_MOUNTS -v /usr/local/Ascend/driver:/usr/local/Ascend/driver"
        echo "✅ 将挂载: /usr/local/Ascend/driver"
    fi

    # 挂载整个Ascend目录（更安全的方式）
    if [ -d "/usr/local/Ascend" ]; then
        ASCEND_MOUNTS="$ASCEND_MOUNTS -v /usr/local/Ascend:/usr/local/Ascend"
        echo "✅ 将挂载: /usr/local/Ascend (完整目录)"
    fi

    # 挂载npu-smi工具
    if [ -f "/usr/local/bin/npu-smi" ]; then
        ASCEND_MOUNTS="$ASCEND_MOUNTS -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi"
        echo "✅ 将挂载: /usr/local/bin/npu-smi"
    fi

    # 挂载dcmi目录
    if [ -d "/usr/local/dcmi" ]; then
        ASCEND_MOUNTS="$ASCEND_MOUNTS -v /usr/local/dcmi:/usr/local/dcmi"
        echo "✅ 将挂载: /usr/local/dcmi"
    fi

    # 基于官方示例的启动命令，但不运行bash
    # 使用host网络模式，服务会直接在宿主机的9527端口上运行
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --privileged \
        --network=host \
        --shm-size=128G \
        -v $(pwd):/work \
        -w=/work \
        $ASCEND_MOUNTS \
        -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
        -e TZ=Asia/Shanghai \
        "$IMAGE_NAME"
        
else
    echo "🔄 启动容器（CPU模式）..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        -p 9527:9527 \
        -e TZ=Asia/Shanghai \
        "$IMAGE_NAME"
fi

# 检查启动结果
if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✅ 容器运行正常"
        
        # 显示容器信息
        echo
        echo "📊 容器状态:"
        docker ps | grep "$CONTAINER_NAME"
        echo
        
        # 测试服务
        echo "🔍 测试服务..."
        SERVICE_URL="http://localhost:9527"

        if [ "$NPU_AVAILABLE" = true ]; then
            echo "ℹ️  NPU模式使用host网络，服务直接在宿主机9527端口运行"
        else
            echo "ℹ️  CPU模式使用端口映射，服务在容器9527端口映射到宿主机9527端口"
        fi
        
        for i in {1..5}; do
            if curl -s "$SERVICE_URL/health" >/dev/null 2>&1; then
                echo "✅ 服务健康检查通过"
                echo "🎉 服务已成功启动!"
                echo
                echo "服务地址: $SERVICE_URL"
                echo "健康检查: $SERVICE_URL/health"
                echo "OCR接口: $SERVICE_URL/ocr (POST)"
                break
            else
                echo "⏳ 等待服务启动... ($i/5)"
                sleep 5
            fi
        done
        
        echo
        echo "📝 常用命令:"
        echo "  查看日志: docker logs $CONTAINER_NAME"
        echo "  查看实时日志: docker logs -f $CONTAINER_NAME"
        echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
        echo "  停止服务: docker stop $CONTAINER_NAME"
        echo
        echo "🧪 测试OCR功能:"
        echo "  curl -X POST -F 'file=@test_image.png' $SERVICE_URL/ocr"
        
    else
        echo "❌ 容器启动失败"
        echo "查看容器日志:"
        docker logs "$CONTAINER_NAME" 2>/dev/null || echo "无法获取日志"
        exit 1
    fi
else
    echo "❌ 容器启动失败"
    exit 1
fi
