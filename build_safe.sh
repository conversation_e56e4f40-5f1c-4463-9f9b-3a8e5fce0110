#!/bin/bash

# 安全构建脚本 - 处理昇腾环境依赖问题

set -e

echo "========================================="
echo "安全构建离线PaddleOCR镜像"
echo "处理昇腾环境依赖问题"
echo "========================================="
echo

# 设置镜像信息
IMAGE_NAME="paddleocr-offline-ascend"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="paddleocr-offline"

echo "🧹 清理现有资源..."

# 停止并删除现有容器
if docker ps -a | grep -q "$CONTAINER_NAME" 2>/dev/null; then
    echo "停止容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    echo "删除容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 删除现有镜像
if docker images | grep -q "$IMAGE_NAME" 2>/dev/null; then
    echo "删除镜像: $FULL_IMAGE_NAME"
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
fi

echo "✅ 清理完成"
echo

# 检查必要文件
echo "🔍 检查必要文件..."
REQUIRED_FILES=("Dockerfile.offline" "paddlex_models.tar.gz" "app_simple.py" "ocr_subprocess.py" "run_simple.py" "requirements_simple.txt")

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ 找到文件: $file"
    fi
done

# 检查模型文件大小
MODEL_SIZE=$(du -h paddlex_models.tar.gz | cut -f1)
echo "📦 模型文件大小: $MODEL_SIZE"

echo
echo "🚀 开始构建镜像: $FULL_IMAGE_NAME"
echo
echo "📝 构建说明:"
echo "  - 构建过程中可能出现昇腾相关警告，这是正常的"
echo "  - 完整功能验证将在运行时进行"
echo "  - 使用稳定版本：PaddlePaddle 3.1.0 + PaddleOCR 3.1.0"
echo

# 记录构建开始时间
BUILD_START=$(date +%s)

# 构建镜像
echo "执行构建命令..."
docker build -f Dockerfile.offline -t "$FULL_IMAGE_NAME" --no-cache .

BUILD_EXIT_CODE=$?
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo
    echo "✅ 镜像构建成功!"
    echo "镜像名称: $FULL_IMAGE_NAME"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images | grep "$IMAGE_NAME"
    echo
    
    # 安全的版本验证（不需要昇腾环境）
    echo "🔍 安全验证镜像内容..."
    docker run --rm "$FULL_IMAGE_NAME" bash -c "
echo '📦 检查已安装的包:'
pip list | grep -E '(paddle|numpy|flask)' || echo '包列表获取失败'

echo
echo '📁 检查模型文件:'
ls -la /root/.paddlex/official_models/ 2>/dev/null || echo '模型目录检查失败'

echo
echo '🔧 检查命令可用性:'
which paddleocr && echo '✅ paddleocr命令可用' || echo '❌ paddleocr命令不可用'
which python && echo '✅ python命令可用' || echo '❌ python命令不可用'

echo
echo '📝 注意：完整的PaddlePaddle功能验证需要在有昇腾环境的运行时进行'
"
    
    echo
    echo "🎉 构建完成!"
    echo
    echo "⚠️  重要提醒:"
    echo "   构建过程中的昇腾相关警告是正常的"
    echo "   完整功能将在部署到昇腾服务器后可用"
    echo
    echo "📋 下一步操作:"
    echo
    echo "1. 立即部署测试:"
    echo "   ./quick_deploy_fixed.sh"
    echo
    echo "2. 手动启动测试:"
    echo "   docker run -d --name paddleocr-offline \\"
    echo "     --privileged --network=host --shm-size=128G \\"
    echo "     -v /usr/local/Ascend:/usr/local/Ascend \\"
    echo "     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \\"
    echo "     -v /usr/local/dcmi:/usr/local/dcmi \\"
    echo "     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" \\"
    echo "     $FULL_IMAGE_NAME"
    echo
    echo "3. 验证服务:"
    echo "   curl http://localhost:9527/health"
    echo "   curl -X POST -F 'file=@test_image.jpg' http://localhost:9527/ocr"
    echo
    echo "4. 导出镜像用于其他服务器:"
    echo "   docker save -o paddleocr-offline-safe.tar $FULL_IMAGE_NAME"
    
else
    echo
    echo "❌ 镜像构建失败!"
    echo "构建时间: ${BUILD_TIME}秒"
    echo
    echo "🔧 故障排除建议:"
    echo "1. 检查网络连接是否正常"
    echo "2. 确保有足够的磁盘空间（至少10GB）"
    echo "3. 检查模型文件是否完整"
    echo "4. 查看上面的详细错误信息"
    echo
    echo "如果错误与昇腾环境相关，这可能是正常的构建时警告"
    echo "请检查具体的错误信息来判断是否为真正的构建失败"
    exit 1
fi
