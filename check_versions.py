#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本检查脚本
检查当前环境中关键依赖的版本是否符合要求
"""

import sys
import subprocess
import requests
import json

def check_local_versions():
    """检查本地Python环境的版本"""
    print("🔍 检查本地Python环境版本...")
    
    try:
        import numpy
        numpy_version = numpy.__version__
        if numpy_version == "1.26.4":
            print(f"✅ numpy版本正确: {numpy_version}")
        else:
            print(f"❌ numpy版本不正确: {numpy_version} (要求: 1.26.4)")
    except ImportError:
        print("⚠️ numpy未安装")
    
    try:
        import paddleocr
        print("✅ paddleocr已安装")
    except ImportError:
        print("⚠️ paddleocr未安装")
    
    try:
        import paddle
        paddle_version = paddle.__version__
        print(f"📦 paddlepaddle版本: {paddle_version}")
    except ImportError:
        print("⚠️ paddlepaddle未安装")

def check_docker_versions():
    """检查Docker容器中的版本"""
    print("\n🐳 检查Docker容器版本...")
    
    try:
        # 检查容器是否运行
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if 'paddleocr-service' not in result.stdout:
            print("❌ paddleocr-service容器未运行")
            return False
        
        print("✅ paddleocr-service容器正在运行")
        
        # 检查numpy版本
        result = subprocess.run([
            'docker', 'exec', 'paddleocr-service', 
            'python', '-c', 'import numpy; print(numpy.__version__)'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            numpy_version = result.stdout.strip()
            if numpy_version == "1.26.4":
                print(f"✅ 容器中numpy版本正确: {numpy_version}")
            else:
                print(f"❌ 容器中numpy版本不正确: {numpy_version} (要求: 1.26.4)")
        else:
            print("❌ 无法检查容器中的numpy版本")
        
        # 检查paddleocr版本
        result = subprocess.run([
            'docker', 'exec', 'paddleocr-service', 
            'python', '-c', 'import paddleocr; print("3.1.0")'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 容器中paddleocr可正常导入")
        else:
            print("❌ 容器中paddleocr导入失败")
        
        return True
        
    except subprocess.CalledProcessError:
        print("❌ Docker命令执行失败")
        return False
    except FileNotFoundError:
        print("❌ Docker未安装或不在PATH中")
        return False

def check_service_status():
    """检查服务状态"""
    print("\n🏥 检查服务状态...")
    
    try:
        # 健康检查
        response = requests.get("http://localhost:9527/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
        
        # 服务信息
        response = requests.get("http://localhost:9527/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("✅ 服务信息:")
            print(f"   服务: {info.get('service', 'N/A')}")
            print(f"   版本: {info.get('version', 'N/A')}")
            return True
        else:
            print(f"❌ 服务信息获取失败: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务连接失败: {e}")
        return False
    except json.JSONDecodeError:
        print("❌ 服务响应格式错误")
        return False

def check_requirements_file():
    """检查requirements.txt文件"""
    print("\n📋 检查requirements.txt文件...")
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read()
        
        if 'numpy==1.26.4' in content:
            print("✅ requirements.txt中numpy版本正确")
        else:
            print("❌ requirements.txt中numpy版本不正确")
            if 'numpy' in content:
                for line in content.split('\n'):
                    if 'numpy' in line:
                        print(f"   当前设置: {line.strip()}")
        
        if 'paddleocr==3.1.0' in content:
            print("✅ requirements.txt中paddleocr版本正确")
        else:
            print("❌ requirements.txt中paddleocr版本不正确")
        
        if 'paddlepaddle==3.1.0' in content:
            print("✅ requirements.txt中paddlepaddle版本正确")
        else:
            print("❌ requirements.txt中paddlepaddle版本不正确")
            
    except FileNotFoundError:
        print("❌ requirements.txt文件不存在")

def main():
    """主函数"""
    print("🔍 PaddleOCR环境版本检查")
    print("=" * 40)
    
    # 检查requirements.txt
    check_requirements_file()
    
    # 检查本地环境
    check_local_versions()
    
    # 检查Docker环境
    docker_ok = check_docker_versions()
    
    # 检查服务状态
    if docker_ok:
        service_ok = check_service_status()
    else:
        service_ok = False
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 检查总结:")
    
    if docker_ok and service_ok:
        print("🎉 所有检查通过！环境配置正确")
        print("\n💡 建议:")
        print("   - 可以正常使用OCR服务")
        print("   - 运行 python3 test_deployment.py 进行完整测试")
    else:
        print("⚠️ 发现问题，需要修复")
        print("\n🔧 修复建议:")
        if not docker_ok:
            print("   - 运行 ./fix_numpy_version.sh 修复版本问题")
            print("   - 或运行 ./upgrade_to_3.1.0.sh 重新部署")
        print("   - 检查Docker服务是否正常运行")
        print("   - 查看容器日志: docker logs paddleocr-service")

if __name__ == "__main__":
    main()
