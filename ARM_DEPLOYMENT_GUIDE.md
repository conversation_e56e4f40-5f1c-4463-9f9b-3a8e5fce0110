# ARM昇腾服务器部署指南

本指南提供了在ARM昇腾服务器上部署PaddleOCR服务的完整流程。

## 版本信息

- **基础镜像**: `ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84`
- **PaddlePaddle**: 3.1.0
- **paddle-custom-npu**: 3.1.0  
- **PaddleOCR**: 3.1.0
- **NumPy**: 1.26.4
- **CANN**: 8.0.0 (官方镜像内置)

## 环境要求

### ARM服务器要求
- ARM64架构 (aarch64)
- 昇腾NPU设备 (如昇腾910B)
- 已安装昇腾驱动
- Docker环境

### 昇腾环境组件
确保以下昇腾组件存在：
- `/usr/local/Ascend/driver/` - 昇腾驱动目录
- `/usr/local/bin/npu-smi` - NPU管理工具
- `/usr/local/dcmi/` - DCMI组件目录

## 环境诊断

在部署之前，建议先运行环境诊断脚本检查昇腾环境：

```bash
# 下载并运行诊断脚本
chmod +x diagnose_ascend_env.sh
./diagnose_ascend_env.sh
```

诊断脚本会检查：
- 昇腾目录结构
- 关键库文件（如libmsprofiler.so）
- NPU工具和命令
- 环境变量配置
- 生成推荐的Docker挂载命令

## 部署步骤

### 方案一：在Windows机器上构建，然后部署到ARM服务器

#### 1. 在Windows机器上构建ARM镜像

```bash
# 克隆或下载项目到Windows机器
cd paddleocr-docker

# 给脚本执行权限（如果在WSL中）
chmod +x build_official_ascend.sh

# 构建ARM64镜像
./build_official_ascend.sh
```

#### 2. 导出镜像

```bash
# 导出镜像为tar文件
docker save -o paddleocr-official-ascend.tar paddleocr-official-ascend:latest
```

#### 3. 传输到ARM服务器

```bash
# 使用scp传输到ARM服务器
scp paddleocr-official-ascend.tar user@arm-server:/tmp/

# 或使用其他方式传输文件
```

#### 4. 在ARM服务器上导入和部署

```bash
# 在ARM服务器上导入镜像
docker load -i /tmp/paddleocr-official-ascend.tar

# 下载部署脚本到ARM服务器
wget https://raw.githubusercontent.com/your-repo/paddleocr-docker/main/deploy_arm_server.sh
chmod +x deploy_arm_server.sh

# 执行部署
./deploy_arm_server.sh
```

### 方案二：直接在ARM服务器上构建和部署

#### 1. 在ARM服务器上克隆项目

```bash
git clone https://github.com/your-repo/paddleocr-docker.git
cd paddleocr-docker
```

#### 2. 构建镜像

```bash
chmod +x build_official_ascend.sh
./build_official_ascend.sh
```

#### 3. 部署服务

```bash
chmod +x deploy_arm_server.sh
./deploy_arm_server.sh
```

## 手动部署命令

如果不使用自动化脚本，可以手动执行以下命令：

### 1. 运行容器（完整NPU支持 - 官方推荐方式）

```bash
docker run -d \
  --name paddleocr-official-ascend \
  --restart unless-stopped \
  --privileged \
  --network=host \
  --shm-size=128G \
  -v $(pwd):/work \
  -w=/work \
  -v /usr/local/Ascend:/usr/local/Ascend \
  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
  -v /usr/local/dcmi:/usr/local/dcmi \
  -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
  -e TZ=Asia/Shanghai \
  paddleocr-official-ascend:latest
```

**注意**:
- NPU模式使用`--network=host`，服务会直接在宿主机的9527端口上运行，无需端口映射
- 挂载完整的`/usr/local/Ascend`目录确保包含所有必要的库文件（如libmsprofiler.so）

### 2. 运行容器（CPU模式，无NPU环境）

```bash
docker run -d \
  --name paddleocr-official-ascend \
  --restart unless-stopped \
  -p 9527:9527 \
  -e TZ=Asia/Shanghai \
  paddleocr-official-ascend:latest
```

## 服务测试

### 1. 健康检查

```bash
curl http://localhost:9527/health
```

预期响应：
```json
{"status": "ok"}
```

### 2. OCR功能测试

```bash
# 测试图片OCR
curl -X POST -F 'file=@test_image.png' http://localhost:9527/ocr

# 测试PDF OCR
curl -X POST -F 'file=@test_document.pdf' http://localhost:9527/ocr
```

## 监控和维护

### 查看容器状态

```bash
docker ps | grep paddleocr-official-ascend
```

### 查看服务日志

```bash
# 查看最新日志
docker logs paddleocr-official-ascend

# 实时查看日志
docker logs -f paddleocr-official-ascend
```

### 进入容器调试

```bash
docker exec -it paddleocr-official-ascend bash
```

### 重启服务

```bash
docker restart paddleocr-official-ascend
```

### 停止服务

```bash
docker stop paddleocr-official-ascend
```

### 删除容器

```bash
docker stop paddleocr-official-ascend
docker rm paddleocr-official-ascend
```

## 性能优化

### NPU加速验证

在容器内执行以下命令验证NPU是否正常工作：

```bash
docker exec -it paddleocr-official-ascend python -c "
import paddle
print('Paddle version:', paddle.__version__)
print('Available devices:', paddle.device.get_all_custom_device_type())
try:
    paddle.set_device('npu:0')
    print('NPU device set successfully')
except:
    print('NPU device not available')
"
```

### 内存优化

如果遇到内存问题，可以调整以下环境变量：

```bash
docker run -d \
  --name paddleocr-official-ascend \
  -p 9527:9527 \
  --device=/dev/davinci0:/dev/davinci0 \
  --device=/dev/davinci_manager:/dev/davinci_manager \
  --device=/dev/devmm_svm:/dev/devmm_svm \
  --device=/dev/hisi_hdc:/dev/hisi_hdc \
  -v /usr/local/Ascend:/usr/local/Ascend:ro \
  -e FLAGS_fraction_of_gpu_memory_to_use=0.3 \
  -e FLAGS_initial_gpu_memory_in_mb=512 \
  paddleocr-official-ascend:latest
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查昇腾环境组件是否存在
   - 检查昇腾驱动是否正确安装
   - 查看容器日志：`docker logs paddleocr-official-ascend`

2. **NPU不可用**
   - 验证昇腾驱动安装：`ls -la /usr/local/Ascend/driver/`
   - 检查npu-smi工具：`npu-smi info`
   - 确认CANN版本兼容性

3. **libmsprofiler.so缺失错误**
   ```
   libmsprofiler.so: cannot open shared object file: No such file or directory
   ```
   **解决方案**：
   - 确保挂载完整的`/usr/local/Ascend`目录
   - 运行诊断脚本：`./diagnose_ascend_env.sh`
   - 检查库文件：`find /usr/local -name "libmsprofiler.so*"`
   - 确保使用正确的挂载命令（挂载整个Ascend目录而不是单独的driver目录）

3. **服务响应慢**
   - 检查是否使用了NPU加速
   - 调整内存分配参数
   - 监控系统资源使用情况

4. **OCR识别失败**
   - 检查上传文件格式是否支持
   - 验证文件大小是否超限
   - 查看详细错误日志

### 日志分析

关键日志信息：
- `✅ 检测到NPU设备类型支持` - NPU设备检测成功
- `🚀 使用官方推荐的NPU模式初始化PaddleOCR` - NPU模式启用
- `✅ PaddleOCR初始化成功，使用设备: npu:0` - OCR引擎初始化成功

## 联系支持

如果遇到问题，请提供以下信息：
1. 系统架构：`uname -m`
2. 昇腾设备信息：`ls -la /dev/davinci*`
3. 容器日志：`docker logs paddleocr-official-ascend`
4. 错误详细信息
