version: '3.8'

# 自定义Docker Compose配置
# 专门针对davinci4和davinci5设备的配置

services:
  paddleocr-ascend:
    build:
      context: .
      dockerfile: Dockerfile.ascend
    container_name: paddleocr-ascend-service
    ports:
      - "9527:9527"
    volumes:
      - ./:/app
      # 挂载昇腾设备和驱动库
      - /usr/local/Ascend:/usr/local/Ascend:ro
      - /var/log/npu:/var/log/npu
    devices:
      # 您服务器上的实际昇腾NPU设备
      - /dev/davinci4:/dev/davinci4
      - /dev/davinci5:/dev/davinci5
      
      # 昇腾管理设备（通常固定）
      - /dev/davinci_manager:/dev/davinci_manager
      - /dev/devmm_svm:/dev/devmm_svm
      - /dev/hisi_hdc:/dev/hisi_hdc
    environment:
      - ASCEND_OPP_PATH=/usr/local/Ascend/ascend-toolkit/latest/opp
      - ASCEND_AICPU_PATH=/usr/local/Ascend/ascend-toolkit/latest
      - LD_LIBRARY_PATH=/usr/local/Ascend/ascend-toolkit/latest/lib64
      - PATH=/usr/local/Ascend/ascend-toolkit/latest/bin:$PATH
      - FLAGS_selected_npus=4  # 使用davinci4作为主设备
      - FLAGS_allocator_strategy=auto_growth
    restart: unless-stopped
    privileged: true
    network_mode: host
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9527/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
