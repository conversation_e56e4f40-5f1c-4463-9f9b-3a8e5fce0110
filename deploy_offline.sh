#!/bin/bash

# 离线环境部署脚本
# 用于在无网络的ARM昇腾服务器上部署PaddleOCR服务

set -e

echo "========================================="
echo "离线PaddleOCR服务部署脚本"
echo "========================================="
echo

# 检查是否为ARM64架构
ARCH=$(uname -m)
echo "当前系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "❌ 错误: 此脚本仅适用于ARM64架构的服务器"
    exit 1
fi

# 检查Docker是否安装和运行
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker服务"
    echo "   sudo systemctl start docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查昇腾环境
echo "🔍 检查昇腾NPU环境..."
NPU_ENV_FOUND=false

ASCEND_PATHS=("/usr/local/Ascend" "/usr/local/bin/npu-smi" "/usr/local/dcmi")
for path in "${ASCEND_PATHS[@]}"; do
    if [ -e "$path" ]; then
        echo "✅ 找到昇腾组件: $path"
        NPU_ENV_FOUND=true
    else
        echo "⚠️  未找到组件: $path"
    fi
done

if [ "$NPU_ENV_FOUND" = false ]; then
    echo "⚠️  警告: 未检测到完整的昇腾NPU环境，服务将以CPU模式运行"
    read -p "是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 1
    fi
fi

# 设置镜像和容器信息
IMAGE_NAME="paddleocr-offline-ascend:latest"
CONTAINER_NAME="paddleocr-offline"

# 检查镜像是否存在
if ! docker images | grep -q "paddleocr-offline-ascend"; then
    echo "❌ 错误: 未找到离线镜像 $IMAGE_NAME"
    echo
    echo "请先导入镜像:"
    echo "  docker load -i paddleocr-offline-ascend.tar"
    echo
    echo "或者从其他地方获取镜像文件"
    exit 1
fi

echo "✅ 找到离线镜像: $IMAGE_NAME"

# 停止并删除现有容器（如果存在）
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🔄 停止并删除现有容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# 构建Docker运行命令
DOCKER_CMD="docker run -d"
DOCKER_CMD="$DOCKER_CMD --name $CONTAINER_NAME"
DOCKER_CMD="$DOCKER_CMD --restart unless-stopped"

# NPU模式配置
if [ "$NPU_ENV_FOUND" = true ]; then
    echo "🚀 配置NPU模式启动..."
    DOCKER_CMD="$DOCKER_CMD --privileged"
    DOCKER_CMD="$DOCKER_CMD --network=host"
    DOCKER_CMD="$DOCKER_CMD --shm-size=128G"
    
    # 挂载昇腾环境
    DOCKER_CMD="$DOCKER_CMD -v /usr/local/Ascend:/usr/local/Ascend"
    DOCKER_CMD="$DOCKER_CMD -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi"
    DOCKER_CMD="$DOCKER_CMD -v /usr/local/dcmi:/usr/local/dcmi"
    DOCKER_CMD="$DOCKER_CMD -e ASCEND_RT_VISIBLE_DEVICES=0,1,2,3,4,5,6,7"
    
    echo "✅ NPU环境配置完成"
else
    echo "🔄 配置CPU模式启动..."
    DOCKER_CMD="$DOCKER_CMD -p 9527:9527"
fi

# 添加其他环境变量
DOCKER_CMD="$DOCKER_CMD -e TZ=Asia/Shanghai"
DOCKER_CMD="$DOCKER_CMD $IMAGE_NAME"

echo "🚀 启动离线容器..."
echo "执行命令: $DOCKER_CMD"
echo

# 执行Docker运行命令
eval $DOCKER_CMD

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    echo
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 15
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✅ 容器运行正常"
        
        # 测试健康检查
        echo "🔍 测试服务健康状态..."
        SERVICE_URL="http://localhost:9527"
        
        for i in {1..5}; do
            if curl -s "$SERVICE_URL/health" >/dev/null 2>&1; then
                echo "✅ 服务健康检查通过"
                break
            else
                echo "⏳ 等待服务启动... ($i/5)"
                sleep 5
            fi
        done
        
        # 显示服务信息
        echo
        echo "🎉 离线部署完成!"
        echo "服务地址: $SERVICE_URL"
        echo "健康检查: $SERVICE_URL/health"
        echo "服务信息: $SERVICE_URL/info"
        echo "OCR接口: $SERVICE_URL/ocr (POST)"
        echo
        echo "📊 容器状态:"
        docker ps | grep "$CONTAINER_NAME"
        echo
        echo "📝 常用命令:"
        echo "  查看日志: docker logs $CONTAINER_NAME"
        echo "  查看实时日志: docker logs -f $CONTAINER_NAME"
        echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
        echo "  停止服务: docker stop $CONTAINER_NAME"
        echo "  重启服务: docker restart $CONTAINER_NAME"
        echo
        echo "🧪 测试OCR功能:"
        echo "  curl -X POST -F 'file=@test_image.png' $SERVICE_URL/ocr"
        echo
        echo "✨ 特性:"
        echo "  ✅ 离线运行，无需网络连接"
        echo "  ✅ 包含所有预下载的OCR模型"
        echo "  ✅ 支持NPU加速（如果环境支持）"
        echo "  ✅ 开箱即用"
        
    else
        echo "❌ 容器启动失败"
        echo "查看容器日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
else
    echo "❌ 容器启动失败"
    exit 1
fi
