#!/bin/bash

# 昇腾驱动检查脚本
# 用于检查宿主机昇腾驱动安装状态

echo "========================================="
echo "昇腾驱动检查脚本"
echo "========================================="
echo

# 检查系统架构
ARCH=$(uname -m)
echo "系统架构: $ARCH"

if [ "$ARCH" != "aarch64" ]; then
    echo "⚠️  警告: 当前系统不是ARM64架构，昇腾910B需要ARM64系统"
fi
echo

# 检查昇腾驱动模块
echo "检查昇腾驱动模块..."
DRIVER_MODULES="drv_devmng drv_devmm_svm drv_hdc_host"
DRIVER_LOADED=0

for module in $DRIVER_MODULES; do
    if lsmod | grep -q "$module"; then
        echo "✓ 驱动模块已加载: $module"
        DRIVER_LOADED=1
    else
        echo "❌ 驱动模块未加载: $module"
    fi
done

if [ $DRIVER_LOADED -eq 0 ]; then
    echo "❌ 未检测到任何昇腾驱动模块"
else
    echo "✓ 检测到昇腾驱动模块"
fi
echo

# 检查设备文件
echo "检查昇腾设备文件..."
DEVICE_FOUND=0
DEVICES=""

# 检查NPU设备
for i in {0..7}; do
    if [ -e "/dev/davinci$i" ]; then
        echo "✓ 发现NPU设备: /dev/davinci$i"
        ls -la /dev/davinci$i
        DEVICES="$DEVICES davinci$i"
        DEVICE_FOUND=1
    fi
done

# 检查管理设备
MGMT_DEVICES="davinci_manager devmm_svm hisi_hdc"
for device in $MGMT_DEVICES; do
    if [ -e "/dev/$device" ]; then
        echo "✓ 发现管理设备: /dev/$device"
        ls -la /dev/$device
        DEVICE_FOUND=1
    else
        echo "❌ 缺少管理设备: /dev/$device"
    fi
done

if [ $DEVICE_FOUND -eq 0 ]; then
    echo "❌ 未检测到任何昇腾设备文件"
else
    echo "✓ 检测到昇腾设备文件"
fi
echo

# 检查昇腾目录结构
echo "检查昇腾安装目录..."
ASCEND_DIRS="/usr/local/Ascend /usr/local/dcmi"

for dir in $ASCEND_DIRS; do
    if [ -d "$dir" ]; then
        echo "✓ 发现昇腾目录: $dir"
        echo "  目录内容:"
        ls -la "$dir" | head -10
    else
        echo "❌ 未找到昇腾目录: $dir"
    fi
done
echo

# 检查npu-smi工具
echo "检查npu-smi工具..."
NPU_SMI_PATHS="/usr/local/Ascend/driver/tools/npu-smi /usr/local/dcmi/bin/npu-smi"

NPU_SMI_FOUND=""
for path in $NPU_SMI_PATHS; do
    if [ -x "$path" ]; then
        echo "✓ 发现npu-smi工具: $path"
        NPU_SMI_FOUND="$path"
        break
    fi
done

if [ -n "$NPU_SMI_FOUND" ]; then
    echo "尝试运行npu-smi info..."
    if $NPU_SMI_FOUND info 2>/dev/null; then
        echo "✓ npu-smi运行成功"
    else
        echo "⚠️  npu-smi运行失败，可能需要管理员权限"
    fi
else
    echo "❌ 未找到npu-smi工具"
fi
echo

# 检查驱动版本信息
echo "检查驱动版本信息..."
VERSION_FILES="/usr/local/Ascend/driver/version.info /usr/local/dcmi/version.info"

for file in $VERSION_FILES; do
    if [ -f "$file" ]; then
        echo "✓ 发现版本文件: $file"
        echo "  版本信息:"
        cat "$file" | head -5
        echo
    fi
done

# 总结检查结果
echo "========================================="
echo "检查结果总结"
echo "========================================="

if [ $DRIVER_LOADED -eq 1 ] && [ $DEVICE_FOUND -eq 1 ]; then
    echo "✅ 昇腾驱动检查通过"
    echo "   - 驱动模块已加载"
    echo "   - 设备文件存在"
    echo "   - 可以继续进行Docker部署"
    echo
    echo "建议的Docker运行命令:"
    echo "docker run --name paddleocr-ascend \\"
    for device in $DEVICES; do
        echo "  --device=/dev/$device:/dev/$device \\"
    done
    for mgmt in $MGMT_DEVICES; do
        if [ -e "/dev/$mgmt" ]; then
            echo "  --device=/dev/$mgmt:/dev/$mgmt \\"
        fi
    done
    echo "  -v /usr/local/Ascend:/usr/local/Ascend:ro \\"
    echo "  -p 9527:9527 paddleocr-ascend:latest"
    
elif [ $DRIVER_LOADED -eq 0 ] && [ $DEVICE_FOUND -eq 0 ]; then
    echo "❌ 昇腾驱动未安装"
    echo "   需要安装以下组件:"
    echo "   1. 昇腾NPU驱动 (Ascend-hdk-910b-npu-driver_xxx_linux-aarch64.run)"
    echo "   2. 昇腾NPU固件 (Ascend-hdk-910b-npu-firmware_xxx_linux-aarch64.run)"
    echo
    echo "安装步骤:"
    echo "1. 从华为昇腾官网下载对应的驱动包"
    echo "2. 安装系统依赖:"
    echo "   sudo apt update"
    echo "   sudo apt install -y linux-headers-\$(uname -r) linux-headers-generic"
    echo "   sudo apt install -y build-essential dkms gcc make"
    echo "3. 检查并修复权限配置:"
    echo "   sudo rm -f /etc/ascend_install.info"
    echo "   echo 'install_username=root' | sudo tee /etc/ascend_install.info"
    echo "4. 以管理员权限安装驱动:"
    echo "   sudo chmod +x Ascend-hdk-910b-npu-driver_*.run"
    echo "   sudo ./Ascend-hdk-910b-npu-driver_*.run --full"
    echo "   # 如果提示输入内核路径，输入：/lib/modules/\$(uname -r)/build"
    echo "5. 安装固件:"
    echo "   sudo chmod +x Ascend-hdk-910b-npu-firmware_*.run"
    echo "   sudo ./Ascend-hdk-910b-npu-firmware_*.run --full"
    echo "6. 重启系统"
    echo "7. 重新运行此检查脚本"
    
else
    echo "⚠️  昇腾驱动部分安装"
    echo "   请检查驱动安装是否完整"
    echo "   可能需要重新安装或重启系统"
fi

echo
echo "更多信息请参考:"
echo "- 华为昇腾官网: https://www.hiascend.com"
echo "- 驱动安装指南: https://www.hiascend.com/document"
echo "- 项目文档: CANN_INSTALLATION.md"
